# 邮件重复发送问题解决方案

## 问题背景

在 `NetValueDisclosureService.java` 的 `sync` 方法中，由于并发执行和状态更新时机问题，导致邮件被重复发送，特别是在分钟级别的时间间隔内出现重复发送。

## 核心问题

1. **状态更新滞后**：查询到需要发送的邮件后，在启动任务前没有立即更新状态
2. **并发竞争**：多个定时任务或手动触发可能同时执行
3. **净值披露特殊性**：净值披露邮件没有 `SENDING` 状态，只有 0（未披露）和 1（已披露）

## 解决方案

### 方案一：普通邮件（有 SENDING 状态的邮件）

**适用邮件类型**：托管对账邮件、估值表邮件、报表邮件、三方邮件

**核心思路**：在启动邮件发送任务前，立即将邮件状态从 `UNSENT` 更新为 `SENDING`

#### 实现步骤

1. **修改 `doSendMailSimple` 方法**
```java
private void doSendMailSimple(List<NetValueDisclosure> dataList, String dataDate, List<String> jobIds,
        boolean hasBalance) {
    if (!dataList.isEmpty()) {
        // 先将状态设置为SENDING，防止重复发送
        updateMailStatusToSending(dataList, jobIds);
        
        // 然后启动邮件发送任务
        // ... 其他代码保持不变
    }
}
```

2. **添加状态更新方法**
```java
private void updateMailStatusToSending(List<NetValueDisclosure> dataList, List<String> jobIds) {
    String jobClassName = cronService.getJobs(jobIds).get(0).getClassName();
    
    LambdaUpdateWrapper<NetValueDisclosure> updateWrapper = new LambdaUpdateWrapper<>();
    updateWrapper.in(NetValueDisclosure::getId, 
        dataList.stream().map(NetValueDisclosure::getId).collect(Collectors.toList()));
    
    switch (jobClassName) {
        case "CustodianBankMailSendJob":
            updateWrapper.set(NetValueDisclosure::getCustodyReconciliationEmailSent, MailStatus.SENDING.name());
            break;
        case "InvestorMailSendJob":
            updateWrapper.set(NetValueDisclosure::getValuationTableSent, MailStatus.SENDING.name());
            break;
        case "InvestorStatementMailSendJob":
            updateWrapper.set(NetValueDisclosure::getInvestorReportSent, MailStatus.SENDING.name());
            break;
        case "ThirdMailSendJob":
            updateWrapper.set(NetValueDisclosure::getThirdPartySent, MailStatus.SENDING.name());
            break;
    }
    
    this.update(updateWrapper);
}
```

#### 状态流转
- 发送前：`UNSENT` → `SENDING`
- 发送成功：`SENDING` → `SUCCESS`
- 发送失败：`SENDING` → `FAILED`

### 方案二：净值披露邮件（无 SENDING 状态）

**核心思路**：使用数据库乐观锁机制，通过原子性的状态更新防止重复

#### 实现方法

```java
public void sendNetValueDisclosureSimple(List<NetValueDisclosure> dataList, String dataDate) {
    if (!dataList.isEmpty()) {
        // 使用乐观锁机制，逐个尝试锁定记录
        List<NetValueDisclosure> lockedRecords = new ArrayList<>();
        List<String> productIds = dataList.stream().map(NetValueDisclosure::getProductId).collect(Collectors.toList());

        for (String productId : productIds) {
            // 使用乐观锁尝试将状态从0更新为-1（表示正在处理中）
            LambdaUpdateWrapper<NetValueDisclosure> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(NetValueDisclosure::getValuationDate, dataDate);
            updateWrapper.eq(NetValueDisclosure::getProductId, productId);
            updateWrapper.eq(NetValueDisclosure::getNetValueDisclosed, 0); // 乐观锁条件
            updateWrapper.set(NetValueDisclosure::getNetValueDisclosed, 2); // 设置为处理中状态

            boolean updated = this.update(updateWrapper);
            if (updated) {
                // 更新成功，重新查询这条记录
                NetValueDisclosure lockedRecord = this.getOne(queryWrapper);
                if (lockedRecord != null) {
                    lockedRecords.add(lockedRecord);
                }
            }
        }

        if (lockedRecords.isEmpty()) {
            log.info("没有成功锁定的净值披露记录，可能都已被其他进程处理");
            return;
        }

        // 使用锁定的记录发送邮件
        // ... 启动邮件发送任务
    }
}
```

#### 状态定义
- `0`：未披露（初始状态）
- `1`：已披露（最终状态）
- `2`：正在处理中（临时锁定状态）

#### 工作原理
1. **乐观锁定**：使用数据库原子操作将状态从 0 更新为 -1
2. **竞争处理**：只有一个进程能成功更新状态，其他进程自动跳过
3. **状态流转**：发送成功时 2 → 1，发送失败时 2 → 0
4. **异常恢复**：发送失败时自动回滚锁定状态

## 技术优势

### 1. 最小化修改
- 只修改核心邮件发送方法
- 不涉及数据库结构变更
- 不影响现有业务逻辑

### 2. 数据库级别保证
- 利用数据库原子性操作
- 无需复杂的锁机制
- 依赖成熟的数据库事务机制

### 3. 分类处理
- 有状态字段的邮件：使用 SENDING 状态
- 无状态字段的邮件：使用乐观锁机制
- 针对性解决，避免过度设计

### 4. 向后兼容
- 完全兼容现有代码
- 可以快速回滚
- 风险极低

## 适用场景

- **分钟级别重复**：完美解决分钟级别的重复发送问题
- **并发环境**：适用于多实例、多线程环境
- **高可靠性要求**：利用数据库保证数据一致性
- **快速部署**：修改量小，可快速上线

## 部署建议

### 1. 测试验证
- 在测试环境充分验证邮件发送功能
- 模拟并发场景测试防重复效果
- 验证异常情况下的状态处理

### 2. 监控要点
- 监控长时间停留在 `SENDING` 状态的记录
- 关注邮件发送成功率
- 观察重复发送是否得到有效控制

### 3. 异常处理
- 定期清理异常状态的记录
- 建立邮件发送状态的监控告警
- 准备快速回滚方案

## 总结

这个解决方案通过最小化的代码修改，有效解决了邮件重复发送问题：

- **普通邮件**：利用 SENDING 状态防重复
- **净值披露邮件**：利用数据库乐观锁防重复
- **零数据库变更**：不需要修改任何表结构
- **高可靠性**：依赖数据库 ACID 特性
- **易于维护**：代码简单，逻辑清晰
- **快速部署**：修改量小，风险可控

该方案特别适合处理分钟级别的重复发送问题，通过数据库级别的一致性保证，提供了稳定可靠的防重复机制。
