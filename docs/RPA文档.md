以下文档描述了自动化服务器的 API 接口。

**基础 URL:** `http://自动化服务器IP:端口/CallFunc.aom`
**请求方式:** `POST`

---

### 3.6.22、获取流程ID

**简要描述**
通过流程全路径获取流程ID

**请求URL**
http://自动化服务器IP:端口/CallFunc.aom

**请求方式**
post

**请求模块名称**
TFlowDM

**请求方法名称**
GetFlowIDByFullPath

**请求参数示例**
```json
[
    {
        "Value":"TFlowDM",
        "Type":4,
        "Name":"{9F8E5ECB-5976-4315-B8F3-43B8502B694D}"
    },
    {
        "Value":"52f9076ee5ee4ff39111ac49f5afee1d",
        "Type":4,
        "Name":"Token"
    },
    {
        "Value":"GetFlowIDByFullPath",
        "Type":4,
        "Name":"{2881E26D-62CE-4937-B4BB-8998440417C4}"
    },
    {
        "Value":"父分组\\子分组\\测试流程",
        "Type":4,
        "Name":"FlowPath"
    }
]
```

**json字段说明**

| 字段名                                  | 必选 | 类型   | 说明                        |
| --------------------------------------- | ---- | ------ | --------------------------- |
| {9F8E5ECB-5976-4315-B8F3-43B8502B694D} | 是   | string | 请求模块名                    |
| Token                                   | 是   | string | 用户令牌                      |
| {2881E26D-62CE-4937-B4BB-8998440417C4} | 是   | string | 请求方法名                    |
| FlowPath                                | 是   | string | 流程全路径(分组1\分组N\流程名称) |

**返回示例**
```json
[
    {
        "Value":"TFlowDM",
        "Type":4,
        "Name":"{9F8E5ECB-5976-4315-B8F3-43B8502B694D}"
    },
    {
        "Value":"B27EE95FC1C84CE09BEDB7E4E42B6C9B",
        "Type":4,
        "Name":"FlowID"
    },
    {
        "Value":"52f9076ee5ee4ff39111ac49f5afee1d",
        "Type":4,
        "Name":"Token"
    },
    {
        "Value":"父分组\\子分组\\测试流程",
        "Type":4,
        "Name":"FlowPath"
    },
    {
        "Value":"GetFlowIDByFullPath",
        "Type":4,
        "Name":"{2881E26D-62CE-4937-B4BB-8998440417C4}"
    },
    {
        "Value":"",
        "Type":4,
        "Name":"{50043442-8A69-4A6B-A8B5-61F882EDE4F3}"
    }
]
```

**返回参数说明**

| 参数名                                  | 类型   | 说明     |
| --------------------------------------- | ------ | -------- |
| {50043442-8A69-4A6B-A8B5-61F882EDE4F3} | string | 错误信息   |
| FlowID                                  | string | 流程ID   |

**备注**
无

---

### 3.6.25、执行流程

**简要描述**
执行流程

**请求URL**
http://自动化服务器IP:端口/CallFunc.aom

**请求方式**
post

**请求模块名称**
TFlowDM

**请求方法名称**
StartFlow

**请求参数示例**

**执行流程（固定机器执行、有组件参数）**
```json
[
    {
        "Value":true,
        "Type":1,
        "Name":"IsThird"
    },
    {
        "Value":false,
        "Type":1,
        "Name":"IsQueue"
    },
    {
        "Value":"395F699697644F30A8D514974EA888AD",
        "Type":4,
        "Name":"FlowID"
    },
    {
        "Value":2,
        "Type":0,
        "Name":"VCLParamCount"
    },
    {
        "Value":"(队列机器).(脚本_有参数)",
        "Type":4,
        "Name":"VCLObjectName0"
    },
    {
        "Value":"'李四',123",
        "Type":4,
        "Name":"VCLParamValue0"
    },
    {
        "Value":"(队列机器).(脚本_有参数2)",
        "Type":4,
        "Name":"VCLObjectName1"
    },
    {
        "Value":"'张三',23",
        "Type":4,
        "Name":"VCLParamValue1"
    },
    {
        "Value":"TFlowDM",
        "Type":4,
        "Name":"{9F8E5ECB-5976-4315-B8F3-43B8502B694D}"
    },
    {
        "Value":"{{Token}}",
        "Type":4,
        "Name":"Token"
    },
    {
        "Value":"StartFlow",
        "Type":4,
        "Name":"{2881E26D-62CE-4937-B4BB-8998440417C4}"
    }
]
```

**执行流程（固定机器执行、有流程参数）**

```json
[
    {
        "Name":"FlowID",
        "Type":4,
        "Value":"56CA11C83CEF473A87E25EA991C623CE"
    },
    {
        "Name":"TimeOut",
        "Type":0,
        "Value":2147483647
    },
    {
        "Name":"IsDesign",
        "Type":1,
        "Value":false
    },
    {
        "Name":"FlowParamsValue_省份",
        "Type":4,
        "Value":"广东"
    },
    {
        "Name":"FlowParamsValue_市区",
        "Type":4,
        "Value":"珠海"
    },
    {
        "Name":"FlowParamsValue_类型",
        "Type":4,
        "Value":"招标"
    },
    {
        "Name":"FlowParamsValue_时间",
        "Type":4,
        "Value":"近半年"
    },
    {
        "Name":"FlowParamsNames",
        "Type":7,
        "Value":"省份\r\n市区\r\n类型\r\n时间"
    },
    {
        "Value":"TFlowDM",
        "Type":4,
        "Name":"{9F8E5ECB-5976-4315-B8F3-43B8502B694D}"
    },
    {
        "Value":"{{Token}}",
        "Type":4,
        "Name":"Token"
    },
    {
        "Value":"StartFlow",
        "Type":4,
        "Name":"{2881E26D-62CE-4937-B4BB-8998440417C4}"
    }
]
```

**执行流程（队列机器执行、有节点参数）**
```json
[
    {
        "Value":"TFlowDM",
        "Type":4,
        "Name":"{9F8E5ECB-5976-4315-B8F3-43B8502B694D}"
    },
    {
        "Value":"{{Token}}",
        "Type":4,
        "Name":"Token"
    },
    {
        "Value":"4A4F1BD4533F4745AA522407DEF4914A",
        "Type":4,
        "Name":"FlowID"
    },
    {
        "Value":"{60E6ADF4-72A2-4A2E-B08D-732D7D55CC39}\r\n{4ACA0B7A-82A1-4C17-B3A7-BE76FB0D3946}",
        "Type":7,
        "Name":"ParamsIDs"
    },
    {
        "Value":"782C992AE8E84477A8992807847DAB1C",
        "Type":4,
        "Name":"R_QueueAgentID"
    },
    {
        "Value":"127.0.0.1",
        "Type":4,
        "Name":"R_QueueAgentName"
    },
    {
        "Value":"姓名\r\n年龄",
        "Type":7,
        "Name":"ParamsNames_{60E6ADF4-72A2-4A2E-B08D-732D7D55CC39}"
    },
    {
        "Value":"姓名1",
        "Type":4,
        "Name":"ParamsValue_{60E6ADF4-72A2-4A2E-B08D-732D7D55CC39}_姓名"
    },
    {
        "Value":"年龄1",
        "Type":4,
        "Name":"ParamsValue_{60E6ADF4-72A2-4A2E-B08D-732D7D55CC39}_年龄"
    },
    {
        "Value":"姓名\r\n年龄",
        "Type":7,
        "Name":"ParamsNames_{4ACA0B7A-82A1-4C17-B3A7-BE76FB0D3946}"
    },
    {
        "Value":"姓名2",
        "Type":4,
        "Name":"ParamsValue_{4ACA0B7A-82A1-4C17-B3A7-BE76FB0D3946}_姓名"
    },
    {
        "Value":"年龄2",
        "Type":4,
        "Name":"ParamsValue_{4ACA0B7A-82A1-4C17-B3A7-BE76FB0D3946}_年龄"
    },
    {
        "Value":"StartFlow",
        "Type":4,
        "Name":"{2881E26D-62CE-4937-B4BB-8998440417C4}"
    }
]
```

**执行流程（将节点参数值，以base64格式上传到服务器，且可通过文件的形式将其下载下来）**
```json
[
    {
        "Value":"TFlowDM",
        "Type":4,
        "Name":"{9F8E5ECB-5976-4315-B8F3-43B8502B694D}"
    },
    {
        "Value":"b5cbf11d69874c73b73104fba172626c",
        "Type":4,
        "Name":"Token"
    },
    {
        "Value":"EBFED01AE91E40B38C1795A6B6413F7F",
        "Type":4,
        "Name":"FlowID"
    },
    {
        "Value":"StartFlow",
        "Type":4,
        "Name":"{2881E26D-62CE-4937-B4BB-8998440417C4}"
    },
    {
        "Value":"true",
        "Type":1,
        "Name":"IsThird"
    },
    {
        "Value":"true",
        "Type":1,
        "Name":"IsQueue"
    },
    {
        "Value":"6L+Z5piv5LiA5Liq5rWL6K+V5pWw5o2u",
        "Type":4,
        "Name":"Param0Value"
    },
    {
        "Value":"参数",
        "Type":4,
        "Name":"Param0Name"
    },
    {
        "Value":1,
        "Type":0,
        "Name":"ParamCount"
    },
    {
        "Value":true,
        "Type":1,
        "Name":"Param0Base64File"
    }
]
```

当执行完该接口后，可在【执行历史】界面找到该条历史记录，单击【查看】进入流程详情界面，可查看到节点参数base64文件保存路径，通过【下载文件】函数，可将该节点参数的值以文件的形式下载下来。如下图所示：（注意：该功能在2023-04-18之后的版本方可使用）

**json字段说明**

| 字段名                       | 必选 | 类型      | 说明                                                              |
| ---------------------------- | ---- | --------- | ----------------------------------------------------------------- |
| {9F8E5ECB-5976-4315-B8F3-43B8502B694D} | 是   | string    | 请求模块名                                                              |
| Token                        | 是   | string    | 用户令牌                                                              |
| {2881E26D-62CE-4937-B4BB-8998440417C4} | 是   | string    | 请求方法名                                                              |
| FlowID                       | 是   | string    | 流程ID                                                              |
| IsThird                      | 否   | boolean   | 是否第三方调用                                                          |
| IsQueue                      | 否   | boolean   | 是否队列执行                                                          |
| VCLParamCount                | 否   | integer   | 组件节点个数。                                                          |
| VCLObjectName%d              | 否   | string    | 第%d个组件节点的名称，节点下标从0开始。可通过GetFlowObejctInfo接口获取 |
| VCLParamValue%d              | 否   | string    | 组件节点运行参数值，多个参数值使用英文逗号（,）分隔                    |
| FlowParamsNames              | 否   | 字符串列表 | 流程参数名称列表，多个参数使用\r\n分隔                                              |
| FlowParamsValue_%s           | 否   | string    | 流程参数数值。其中，%s为流程参数名称                                           |
| ParamsIDs                    | 否   | 字符串列表 | 节点ID列表，节点ID可通过GetFlowObejctInfo接口获取                                   |
| ParamsNames_节点ID         | 否   | string    | 节点参数列表                                                       |
| ParamsValue节点ID节点参数名   | 否   | string    | 节点参数值                                                          |
| R_QueueAgentID               | 否   | string    | Agent ID，节点执行机器为队列机器时使用                                       |
| R_QueueAgentName             | 否   | string    | Agent IP，节点执行机器为队列机器时使用                                        |
| Param%dValue                 | 是   | string    | 代表第N个节点参数值，传入的值为base64格式。（该参数在2023-04-18之后的版本方可使用）  |
| Param%dName                  | 是   | string    | 代表第N个节点参数名称。（该参数在2023-04-18之后的版本方可使用）                                 |
| Param%dBase64File            | 是   | Boolean   | 代表第N个参数是base64文件（该参数在2023-04-18之后的版本方可使用）                                 |

**返回示例**
略

**返回参数说明**

| 参数名                                  | 类型   | 说明     |
| --------------------------------------- | ------ | -------- |
| {50043442-8A69-4A6B-A8B5-61F882EDE4F3} | string | 错误信息   |
| ExecID                                  | string | 执行ID   |

**备注**
无

---

### 3.6.41、通过用户获取全部执行流程

**简要描述**
通过指定用户获取该用户执行的全部流程 （支持同时获取多个用户的流程，多个用户之间英文逗号分隔开）。
通过指定流程ID，查询该流程的执行用户（支持同时查询多个流程的执行用户，多个流程ID之间用英文逗号分隔开）。

**请求URL**
http://自动化服务器IP:端口/CallFunc.aom

**请求方式**
Post

**请求模块名称**
TUserDM

**请求方法名称**
GetFlowExecedStateEx

**请求参数示例**

**通过指定用户查询流程请求示例：**
```json
[
    {
        "Value":"TUserDM",
        "Type":4,
        "Name":"{9F8E5ECB-5976-4315-B8F3-43B8502B694D}"
    },
    {
        "Value":"/",
        "Type":4,
        "Name":"IDs"
    },
    {
        "Value":"admin,user",
        "Type":4,
        "Name":"sExecutorIDs"
    },
    {
        "Value":"2022-09-09",
        "Type":4,
        "Name":"BeginDate"
    },
    {
        "Value":"2022-09-09",
        "Type":4,
        "Name":"EndDate"
    },
    {
        "Value":"8ac2f2d8e306498db5c881ba418daff2",
        "Type":4,
        "Name":"Token"
    },
    {
        "Value":"GetFlowExecedStateEx",
        "Type":4,
        "Name":"{2881E26D-62CE-4937-B4BB-8998440417C4}"
    }
]
```

**通过流程ID查询流程执行人请求示例：**
```json
[
    {
        "Value":"TUserDM",
        "Type":4,
        "Name":"{9F8E5ECB-5976-4315-B8F3-43B8502B694D}"
    },
    {
        "Value":"EBFED01AE91E40B38C1795A6B6413F7F,0C04AD21BBF74673939BA67E0A76F386",
        "Type":4,
        "Name":"IDs"
    },
    {
        "Value":"",
        "Type":4,
        "Name":"sExecutorIDs"
    },
    {
        "Value":"2022-09-09",
        "Type":4,
        "Name":"BeginDate"
    },
    {
        "Value":"2022-09-09",
        "Type":4,
        "Name":"EndDate"
    },
    {
        "Value":"b41ba18cbd0042988cd942973490f0d3",
        "Type":4,
        "Name":"Token"
    },
    {
        "Value":"GetFlowExecedStateEx",
        "Type":4,
        "Name":"{2881E26D-62CE-4937-B4BB-8998440417C4}"
    }
]
```

**json字段说明**

| 字段名                                  | 必选 | 类型   | 说明                                                                    |
| --------------------------------------- | ---- | ------ | ----------------------------------------------------------------------- |
| {9F8E5ECB-5976-4315-B8F3-43B8502B694D} | 是   | string | 请求模块名，固定值为TUserDM                                                |
| Token                                   | 是   | string | 用户令牌，有效时长为10分钟                                                 |
| {2881E26D-62CE-4937-B4BB-8998440417C4} | 是   | string | 请求方法名，固定值为GetFlowExecedStateEx                                 |
| sExecutorIDs                            | 是   | string | 执行人ID（用户），多个用户之间用（英文逗号）隔开                                   |
| BeginDate                               | 是   | string | 开始日期，用-（横杠）                                                         |
| EndDate                                 | 是   | string | 结束日期，用-（横杠）                                                         |
| IDs                                     | 是   | string | 流程ID，多个流程ID之间用（英文逗号）隔开，查询执行人的流程时，IDs也需要输入不能为空，输入的值为’/’。 |

**返回示例**

**通过指定用户查询流程返回示例：**
```json
[
    {
        "Value":[
            [
                {
                    "Type":4,
                    "Name":"ExecID"
                },
                {
                    "Type":4,
                    "Name":"TaskID"
                },
                {
                    "Type":4,
                    "Name":"TaskType"
                },
                {
                    "Type":4,
                    "Name":"ObjectID"
                },
                {
                    "Type":4,
                    "Name":"ObjectName"
                },
                {
                    "Type":4,
                    "Name":"ObjectType"
                },
                {
                    "Type":4,
                    "Name":"ExecTime"
                },
                {
                    "Type":4,
                    "Name":"Enabled"
                },
                {
                    "Type":4,
                    "Name":"MoreTask"
                },
                {
                    "Type":4,
                    "Name":"ExecState"
                },
                {
                    "Type":4,
                    "Name":"BeginTime"
                },
                {
                    "Type":4,
                    "Name":"EndTime"
                },
                {
                    "Type":4,
                    "Name":"Remark"
                },
                {
                    "Type":4,
                    "Name":"InitFlag"
                },
                {
                    "Type":4,
                    "Name":"UserName"
                },
                {
                    "Type":4,
                    "Name":"Version"
                },
                {
                    "Type":4,
                    "Name":"TagID"
                },
                {
                    "Type":4,
                    "Name":"TagName"
                }
            ],
            [
                {
                    "Value":"90449BA8A46645AF9D5A77A5997B1616"
                },
                {
                    "Value":""
                },
                {
                    "Value":"手工执行"
                },
                {
                    "Value":"EBFED01AE91E40B38C1795A6B6413F7F"
                },
                {
                    "Value":"ab"
                },
                {
                    "Value":"流程执行"
                },
                {
                    "Value":"2022-09-0914:15:08"
                },
                {
                    "Value":""
                },
                {
                    "Value":""
                },
                {
                    "Value":"1"
                },
                {
                    "Value":"2022-09-0914:15:08"
                },
                {
                    "Value":"2022-09-0914:15:10"
                },
                {
                    "Value":"执行成功"
                },
                {
                    "Value":""
                },
                {
                    "Value":"admin[Administrator111]"
                },
                {
                    "Value":"5"
                },
                {
                    "Value":""
                },
                {
                    "Value":""
                }
            ],
            [
                {
                    "Value":"DAE7D549BF5748CFB2452CAC00BF9412"
                },
                {
                    "Value":""
                },
                {
                    "Value":"手工执行"
                },
                {
                    "Value":"BD68F7CC033946868C639D46B6DD5DF1"
                },
                {
                    "Value":"重做"
                },
                {
                    "Value":"流程执行"
                },
                {
                    "Value":"2022-09-0915:30:41"
                },
                {
                    "Value":""
                },
                {
                    "Value":""
                },
                {
                    "Value":"1"
                },
                {
                    "Value":"2022-09-0915:30:41"
                },
                {
                    "Value":"2022-09-0915:30:43"
                },
                {
                    "Value":"执行成功"
                },
                {
                    "Value":""
                },
                {
                    "Value":"user[user]"
                },
                {
                    "Value":"8"
                },
                {
                    "Value":""
                },
                {
                    "Value":""
                }
            ]
        ],
        "Type":19,
        "Name":"k_flow"
    },
    {
        "Value":"",
        "Type":4,
        "Name":"{50043442-8A69-4A6B-A8B5-61F882EDE4F3}"
    }
]
```

**通过流程ID查询流程执行人返回示例：**
```json
[
    {
        "Value":[
            [
                {
                    "Type":4,
                    "Name":"ExecID"
                },
                {
                    "Type":4,
                    "Name":"TaskID"
                },
                {
                    "Type":4,
                    "Name":"TaskType"
                },
                {
                    "Type":4,
                    "Name":"ObjectID"
                },
                {
                    "Type":4,
                    "Name":"ObjectName"
                },
                {
                    "Type":4,
                    "Name":"ObjectType"
                },
                {
                    "Type":4,
                    "Name":"ExecTime"
                },
                {
                    "Type":4,
                    "Name":"Enabled"
                },
                {
                    "Type":4,
                    "Name":"MoreTask"
                },
                {
                    "Type":4,
                    "Name":"ExecState"
                },
                {
                    "Type":4,
                    "Name":"BeginTime"
                },
                {
                    "Type":4,
                    "Name":"EndTime"
                },
                {
                    "Type":4,
                    "Name":"Remark"
                },
                {
                    "Type":4,
                    "Name":"InitFlag"
                },
                {
                    "Type":4,
                    "Name":"UserName"
                },
                {
                    "Type":4,
                    "Name":"Version"
                },
                {
                    "Type":4,
                    "Name":"TagID"
                },
                {
                    "Type":4,
                    "Name":"TagName"
                }
            ],
            [
                {
                    "Value":"C9F5ED1BE17C4E0C9CB1FD531B393699"
                },
                {
                    "Value":""
                },
                {
                    "Value":"手工执行"
                },
                {
                    "Value":"0C04AD21BBF74673939BA67E0A76F386"
                },
                {
                    "Value":"代理端缓存"
                },
                {
                    "Value":"流程执行"
                },
                {
                    "Value":"2022-09-0916:17:47"
                },
                {
                    "Value":""
                },
                {
                    "Value":""
                },
                {
                    "Value":"1"
                },
                {
                    "Value":"2022-09-0916:17:47"
                },
                {
                    "Value":"2022-09-0916:17:49"
                },
                {
                    "Value":"执行成功"
                },
                {
                    "Value":""
                },
                {
                    "Value":"admin[Administrator111]"
                },
                {
                    "Value":"11"
                },
                {
                    "Value":""
                },
                {
                    "Value":""
                }
            ],
            [
                {
                    "Value":"90449BA8A46645AF9D5A77A5997B1616"
                },
                {
                    "Value":""
                },
                {
                    "Value":"手工执行"
                },
                {
                    "Value":"EBFED01AE91E40B38C1795A6B6413F7F"
                },
                {
                    "Value":"ab"
                },
                {
                    "Value":"流程执行"
                },
                {
                    "Value":"2022-09-0914:15:08"
                },
                {
                    "Value":""
                },
                {
                    "Value":""
                },
                {
                    "Value":"1"
                },
                {
                    "Value":"2022-09-0914:15:08"
                },
                {
                    "Value":"2022-09-0914:15:10"
                },
                {
                    "Value":"执行成功"
                },
                {
                    "Value":""
                },
                {
                    "Value":"admin[Administrator111]"
                },
                {
                    "Value":"5"
                },
                {
                    "Value":""
                },
                {
                    "Value":""
                }
            ]
        ],
        "Type":19,
        "Name":"k_flow"
    },
    {
        "Value":"",
        "Type":4,
        "Name":"{50043442-8A69-4A6B-A8B5-61F882EDE4F3}"
    }
]
```

**返回参数说明**

| 参数名                                  | 类型   | 说明                                                         |
| --------------------------------------- | ------ | ------------------------------------------------------------ |
| {50043442-8A69-4A6B-A8B5-61F882EDE4F3} | string | 错误信息                                                       |
| K-flow                                  | 表格   | 流程信息                                                       |

**备注**
流程信息表格字段说明：

| 参数名     | 数据类型 | 说明                                                       |
| ---------- | ------ | ---------------------------------------------------------- |
| ExeclID          | string | 执行ID                                                         |
| TaskID           | string | 任务ID                                                         |
| TaskType          | string | 任务类型                                                     |
| ObjectID          | string | 流程ID                                         |
| ObjectName        | string | 流程名称                                                               |
| ObjectType       | string | 执行类型（流程执行或任务执行）                              |
| ExecTime| string | 开始执行时间                                                   |
| Enabled        | string | 是否可用                                                       |
| MoreTask              | string | 一个任务是否可以多次执行                          |
| ExecState          | string | 执行状态（-1：正在执行；0手动停止；1：执行成功；2：执行超时；3：节点异常）   |
| InitFlag| string | 大于0表示节点异常                        |
| BeginTime          | string | 开始时间                                                         |
| EndTime           | string | 结束时间                                                         |
| Remark           | string | 备注                                                                |
| UserName          | string | 用户名                                                          |
| Version | string | 版本                                                       |
| TagID          | string | 标签ID                                         |
| TagName        | string | 标签名称                                                              |

---

### 3.6.39、根据
ExecID获取流程参数

**简要描述**
根据ExecID获取流程参数。

**请求URL**
http://自动化服务器IP:端口/CallFunc.aom

**请求方式**
post

**请求模块名称**
```markdown
TFlowDM

**请求方法名称**
GetBatchRPAChatParam

**请求参数示例**
```json
[
    {
        "Value":"TFlowDM",
        "Type":4,
        "Name":"{9F8E5ECB-5976-4315-B8F3-43B8502B694D}"
    },
    {
        "Value":"GetBatchRPAChatParam",
        "Type":4,
        "Name":"{2881E26D-62CE-4937-B4BB-8998440417C4}"
    },
    {
        "Value":"a91f15ebb5984474b2da2c1e7b765fca",
        "Type":4,
        "Name":"Token"
    },
    {
        "Value":"DE269F18D0E340F7BCE194E750C09CEE",
        "Type":4,
        "Name":"ExecIDs"
    },
    {
        "Value":"20220627",
        "Type":4,
        "Name":"StartDate"
    },
    {
        "Value":"20220628",
        "Type":4,
        "Name":"EndDate"
    }
]
```

**json字段说明**

| 字段名                                  | 必选 | 类型   | 说明                                                 |
| --------------------------------------- | ---- | ------ | ---------------------------------------------------- |
| {9F8E5ECB-5976-4315-B8F3-43B8502B694D} | 是   | string | 请求模块名                                             |
| {2881E26D-62CE-4937-B4BB-8998440417C4} | 是   | string | 请求方法名                                             |
| Token                                   | 是   | string | 用户令牌                                               |
| ExecIDs                                 | 是   | string | 流程明细ID，对应历史库k_flow_detail表中的ID字段        |
| StartDate                               | 是   | string | 开始日期，填写格式为：yyyymmdd                           |
| EndDate                                 | 是   | string | 结束日期，填写格式为：yyyymmdd                           |

**返回示例**
```json
[
    {
        "Value":1,
        "Type":0,
        "Name":"ParamCount_DE269F18D0E340F7BCE194E750C09CEE"
    },
    {
        "Value":true,
        "Type":1,
        "Name":"HistoryFlow_DE269F18D0E340F7BCE194E750C09CEE"
    },
    {
        "Value":"TFlowDM",
        "Type":4,
        "Name":"{9F8E5ECB-5976-4315-B8F3-43B8502B694D}"
    },
    {
        "Value":"参数1",
        "Type":4,
        "Name":"Param0Name_DE269F18D0E340F7BCE194E750C09CEE"
    },
    {
        "Value":"a91f15ebb5984474b2da2c1e7b765fca",
        "Type":4,
        "Name":"Token"
    },
    {
        "Value":"20220628",
        "Type":4,
        "Name":"EndDate"
    },
    {
        "Value":"20220627",
        "Type":4,
        "Name":"StartDate"
    },
    {
        "Value":"DE269F18D0E340F7BCE194E750C09CEE",
        "Type":4,
        "Name":"ExecIDs"
    },
    {
        "Value":"qwe",
        "Type":4,
        "Name":"Param0Value_DE269F18D0E340F7BCE194E750C09CEE"
    },
    {
        "Value":"GetBatchRPAChatParam",
        "Type":4,
        "Name":"{2881E26D-62CE-4937-B4BB-8998440417C4}"
    },
    {
        "Value":"",
        "Type":4,
        "Name":"{50043442-8A69-4A6B-A8B5-61F882EDE4F3}"
    }
]
```

**返回参数说明**

| 参数名                                  | 类型    | 说明                       |
| --------------------------------------- | ------- | -------------------------- |
| Param%dName_ExecIDs                    | string  | 第(%d+1) 个流程参数名          |
| Param%dValue_ExecIDs                   | string  | 第(%d+1) 个流程参数值          |
| ParamCount                              | integer | 流程参数个数                   |
| {50043442-8A69-4A6B-A8B5-61F882EDE4F3} | string  | 错误信息                     |

**备注**

若“开始日期（StartDate）”和“结束日期（EndDate）”两个字段都不传递，则默认获取7天内的数据。

---

### 3.6.40、获取流程所有节点耗时

**简要描述**
获取历史指定流程中所有节点耗时。

**请求URL**
http://自动化服务器IP:端口/CallFunc.aom

**请求方式**
Post

**请求模块名称**
TFlowDM

**请求方法名称**
GetFlowNodeTime

**请求参数示例**
```json
[
    {
        "Value":"TFlowDM",
        "Type":4,
        "Name":"{9F8E5ECB-5976-4315-B8F3-43B8502B694D}"
    },
    {
        "Value":"10cc563c8e584c08ae95eb4bca267ea2",
        "Type":4,
        "Name":"Token"
    },
    {
        "Value":"GetFlowNodeTime",
        "Type":4,
        "Name":"{2881E26D-62CE-4937-B4BB-8998440417C4}"
    },
    {
        "Value":"EBFED01AE91E40B38C1795A6B6413F7F",
        "Type":4,
        "Name":"FlowID"
    },
    {
        "Value":"2022-09-09 14:15:08",
        "Type":4,
        "Name":"BeginTime"
    }
]
```

**json字段说明**

| 字段名                                  | 必选 | 类型   | 说明                                                                    |
| --------------------------------------- | ---- | ------ | ----------------------------------------------------------------------- |
| {9F8E5ECB-5976-4315-B8F3-43B8502B694D} | 是   | string | 请求模块名，固定值为TFlowDM                                                |
| Token                                   | 是   | string | 用户令牌，十分钟有效                                                         |
| {2881E26D-62CE-4937-B4BB-8998440417C4} | 是   | string | 请求方法名，固定值为GetFlowNodeTime                                       |
| FlowID                                  | 否   | string | 流程ID，可通过查询k_flow_info表获取FlowID；也可在【流程设计】界面，选中该流程-鼠标右击-单击属性，流程属性中的ID即此处的FlowID。 |
| BeginTime                               | 是   | string | 流程执行开始时间                                                           |

**返回示例**
```json
[
    {
        "Value":"2022-09-0914:15:08%",
        "Type":4,
        "Name":"BeginTime"
    },
    {
        "Value":[
            [
                {
                    "Type":4,
                    "Name":"FlowID"
                },
                {
                    "Type":4,
                    "Name":"FlowName"
                },
                {
                    "Type":4,
                    "Name":"ExecID"
                },
                {
                    "Type":4,
                    "Name":"ObjName"
                },
                {
                    "Type":4,
                    "Name":"Level"
                },
                {
                    "Type":4,
                    "Name":"Description"
                },
                {
                    "Type":4,
                    "Name":"Value"
                },
                {
                    "Type":4,
                    "Name":"IP"
                },
                {
                    "Type":4,
                    "Name":"ExecTime"
                }
            ],
            [
                {
                    "Value":"EBFED01AE91E40B38C1795A6B6413F7F"
                },
                {
                    "Value":"ab"
                },
                {
                    "Value":"90449BA8A46645AF9D5A77A5997B1616"
                },
                {
                    "Value":"简单输出"
                },
                {
                    "Value":"正常"
                },
                {
                    "Value":"描述"
                },
                {
                    "Value":"数据"
                },
                {
                    "Value":"***************"
                },
                {
                    "Value":"1"
                }
            ]
        ],
        "Type":19,
        "Name":"k_node_info"
    },
    {
        "Value":1,
        "Type":0,
        "Name":"Count"
    },
    {
        "Value":"TFlowDM",
        "Type":4,
        "Name":"{9F8E5ECB-5976-4315-B8F3-43B8502B694D}"
    },
    {
        "Value":"EBFED01AE91E40B38C1795A6B6413F7F",
        "Type":4,
        "Name":"FlowID"
    },
    {
        "Value":"10cc563c8e584c08ae95eb4bca267ea2",
        "Type":4,
        "Name":"Token"
    },
    {
        "Value":"20220909",
        "Type":4,
        "Name":"DBDate"
    },
    {
        "Value":"GetFlowNodeTime",
        "Type":4,
        "Name":"{2881E26D-62CE-4937-B4BB-8998440417C4}"
    },
    {
        "Value":"",
        "Type":4,
        "Name":"{50043442-8A69-4A6B-A8B5-61F882EDE4F3}"
    }
]
```

**返回参数说明**

| 参数名                                  | 类型   | 说明     |
| --------------------------------------- | ------ | -------- |
| {50043442-8A69-4A6B-A8B5-61F882EDE4F3} | string | 错误信息   |
| ExecTime                                | string | 执行时间   |
| k_node_info                             | 表格   | 节点信息表 |

**备注**
k_node_info表字段说明：

| 参数名     | 数据类型 | 说明     |
| ---------- | ------ | -------- |
| FlowID         | string | 流程ID   |
| FlowName        | string | 流程名称 |
| ExecID         | string | 执行ID   |
| ObjName         | string | 流程图名称 |
| Level         | string | 级别   |
| Description         | string | 描述   |
| Value | string | 数据    |
| IP            | string | 代理IP     |
| ExecTime        | string | 执行时间 |

---

### 3.6.50、获取流程截图信息

**简要描述**
获取流程截图信息

**请求URL**
http://自动化服务器IP:端口/CallFunc.aom

**请求方式**
post

**请求模块名称**
TFlowDM

**请求方法名称**
GetFlowScreenshotInfo

**请求参数示例**
```json
[
    {
        "Value":"TFlowDM",
        "Type":4,
        "Name":"{9F8E5ECB-5976-4315-B8F3-43B8502B694D}"
    },
    {
        "Value":"B36190AFA17A57980F755F4DD3ABA579",
        "Type":4,
        "Name":"Token"
    },
    {
        "Value":"GetFlowScreenshotInfo",
        "Type":4,
        "Name":"{2881E26D-62CE-4937-B4BB-8998440417C4}"
    },
    {
        "Value":"B9745C01CE2648B58244F91238E8B87C",
        "Type":4,
        "Name":"ExecID"
    }
]
```

**json字段说明**

| 字段名                                  | 必选 | 类型   | 说明     |
| --------------------------------------- | ---- | ------ | -------- |
| {9F8E5ECB-5976-4315-B8F3-43B8502B694D} | 是   | string | 请求模块名   |
| Token                                   | 是   | string | 用户令牌     |
| {2881E26D-62CE-4937-B4BB-8998440417C4} | 是   | string | 请求方法名   |
| ExecID                                  | 是   | string | 执行ID，对应k_flow_execed表ID字段 |
| DBDate                                  | 否   | String | 开始日期(YYYY-MM-DD), 为空取当天      |

**返回示例**
```json
[
    {
        "Value":"0FDDC411C766409C8831439EB69117D4",
        "Type":4,
        "Name":"0_AgentID"
    },
    {
        "Value":"(192.168.104.84).(自定义)",
        "Type":4,
        "Name":"0_ObjectName"
    },
    {
        "Value":1,
        "Type":0,
        "Name":"Count"
    },
    {
        "Value":"TFlowDM",
        "Type":4,
        "Name":"{9F8E5ECB-5976-4315-B8F3-43B8502B694D}"
    },
    {
        "Value":"B36190AFA17A57980F755F4DD3ABA579",
        "Type":4,
        "Name":"Token"
    },
    {
        "Value":"20230523",
        "Type":4,
        "Name":"DBDate"
    },
    {
        "Value":"B9745C01CE2648B58244F91238E8B87C",
        "Type":4,
        "Name":"ExecID"
    },
    {
        "Value":"GetFlowScreenshotInfo",
        "Type":4,
        "Name":"{2881E26D-62CE-4937-B4BB-8998440417C4}"
    },
    {
        "Value":"0FDDC411C766409C8831439EB69117D4|231545107BFAEA7E67544DB39F85B2896FFA92F0.jpg",
        "Type":4,
        "Name":"0_File"
    },
    {
        "Value":"",
        "Type":4,
        "Name":"{50043442-8A69-4A6B-A8B5-61F882EDE4F3}"
    }
]
```

**返回参数说明**

| 参数名                                  | 类型    | 说明            |
| --------------------------------------- | ------- | --------------- |
| {50043442-8A69-4A6B-A8B5-61F882EDE4F3} | string  | 错误信息          |
| Count                                   | Integer | 图片数量          |
| %d_File                                  | String  | 图片名称AgentID|图片名称 |
| d_ObjectName          | String  | 节点名称          |

**备注**
无

---

### 3.6.51、获取流程截图

**简要描述**
获取流程截图

**请求URL**
http://自动化服务器IP:端口/CallFunc.aom

**请求方式**
post

**请求模块名称**
TFlowDM

**请求方法名称**
GetFlowScreenshot

**请求参数示例**
```json
[
    {
        "Value":"TFlowDM",
        "Type":4,
        "Name":"{9F8E5ECB-5976-4315-B8F3-43B8502B694D}"
    },
    {
        "Value":"B36190AFA17A57980F755F4DD3ABA579",
        "Type":4,
        "Name":"Token"
    },
    {
        "Value":"GetFlowScreenshot",
        "Type":4,
        "Name":"{2881E26D-62CE-4937-B4BB-8998440417C4}"
    },
    {
        "Value":"0FDDC411C766409C8831439EB69117D4",
        "Type":4,
        "Name":"AgentID"
    },
    {
        "Value":"23165450D4C8FFE2300844DBA8C380D591D72FA9.jpg",
        "Type":4,
        "Name":"Name"
    }
]
```

**json字段说明**

| 字段名                                  | 必选 | 类型   | 说明     |
| --------------------------------------- | ---- | ------ | -------- |
| {9F8E5ECB-5976-4315-B8F3-43B8502B694D} | 是   | string | 请求模块名   |
| Token                                   | 是   | string | 用户令牌     |
| {2881E26D-62CE-4937-B4BB-8998440417C4} | 是   | string | 请求方法名   |
| AgentID                                 | 是   | string | 代理ID，对应k_agent表ID字段 |
| Name                                    | 否   | string | 图片名称     |

**返回示例**
略

**返回参数说明**

| 参数名                                  | 类型     | 说明     |
| --------------------------------------- | -------- | -------- |
| {50043442-8A69-4A6B-A8B5-61F882EDE4F3} | string   | 错误信息   |
| Buf                                     | 数据流   | 图片     |
| FileDate                                    | String   | 图片日期     |

**备注**
无

---

### 3.6.52、获取流程录屏信息

**简要描述**
获取流程录屏信息

**请求URL**
http://自动化服务器IP:端口/CallFunc.aom

**请求方式**
post

**请求模块名称**
TFlowDM

**请求方法名称**
GetFlowRecScreenInfo

**请求参数示例**
```json
[
    {
        "Value":"TFlowDM",
        "Type":4,
        "Name":"{9F8E5ECB-5976-4315-B8F3-43B8502B694D}"
    },
    {
        "Value":"B36190AFA17A57980F755F4DD3ABA579",
        "Type":4,
        "Name":"Token"
    },
    {
        "Value":"GetFlowRecScreenInfo",
        "Type":4,
        "Name":"{2881E26D-62CE-4937-B4BB-8998440417C4}"
    },
    {
        "Value":"2CB57CF98E7C4C49AA4B28661A0FE4CC",
        "Type":4,
        "Name":"ExecID"
    }
]
```

**json字段说明**

| 字段名                                  | 必选 | 类型   | 说明     |
| --------------------------------------- | ---- | ------ | -------- |
| {9F8E5ECB-5976-4315-B8F3-43B8502B694D} | 是   | string | 请求模块名   |
| Token                                   | 是   | string | 用户令牌     |
| {2881E26D-62CE-4937-B4BB-8998440417C4} | 是   | string | 请求方法名   |
| ExecID                                  | 是   | string | 执行ID，对应k_flow_execed表ID字段 |
| DBDate                                  | 是   | String | 开始日期(YYYY-MM-DD), 为空取当天    |

**返回示例**
```json
[
    {
        "Value":"(192.168.104.84).(自定义)",
        "Type":4,
        "Name":"0_ObjectName"
    },
    {
        "Value":1,
        "Type":0,
        "Name":"Count"
    },
    {
        "Value":"TFlowDM",
        "Type":4,
        "Name":"{9F8E5ECB-5976-4315-B8F3-43B8502B694D}"
    },
    {
        "Value":"B36190AFA17A57980F755F4DD3ABA579",
        "Type":4,
        "Name":"Token"
    },
    {
        "Value":"20230523",
        "Type":4,
        "Name":"DBDate"
    },
    {
        "Value":"BD79FE78D6124527ABC70081591990A1",
        "Type":4,
        "Name":"ExecID"
    },
    {
        "Value":"GetFlowRecScreenInfo",
        "Type":4,
        "Name":"{2881E26D-62CE-4937-B4BB-8998440417C4}"
    },
    {
        "Value":"D:\\3\\3_20230523_E93916487B3148E7A3F6A31F2C156EAA.mp4",
        "Type":4,
        "Name":"0_File"
    },
    {
        "Value":"",
        "Type":4,
        "Name":"{50043442-8A69-4A6B-A8B5-61F882EDE4F3}"
    }
]
```

**返回参数说明**

| 参数名                                  | 类型    | 说明     |
| --------------------------------------- | ------- | -------- |
| {50043442-8A69-4A6B-A8B5-61F882EDE4F3} | string  | 错误信息   |
| Count                                   | Integer | 录屏数量   |
| %d_File                                  | String  | 文件路径   |
| d_ObjectName          | String  | 节点名称     |

**备注**
无