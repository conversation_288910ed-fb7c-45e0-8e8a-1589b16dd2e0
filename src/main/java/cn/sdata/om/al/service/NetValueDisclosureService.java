package cn.sdata.om.al.service;

import cn.sdata.om.al.constant.BaseConstant;
import cn.sdata.om.al.entity.*;
import cn.sdata.om.al.enums.*;
import cn.sdata.om.al.job.*;
import cn.sdata.om.al.mapper.ValuationDBMapper;
import cn.sdata.om.al.qrtz.entity.CronInfo;
import cn.sdata.om.al.qrtz.service.CronService;
import cn.sdata.om.al.vo.NetValueDisclosureVO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.quartz.JobDataMap;
import org.quartz.JobExecutionContext;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import cn.sdata.om.al.mapper.NetValueDisclosureMapper;

import java.time.LocalTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

import static cn.sdata.om.al.constant.BaseConstant.*;
import static cn.sdata.om.al.constant.JobConstant.*;
import static cn.sdata.om.al.qrtz.constant.CronConstant.SYNC;

@Service
@Slf4j
public class NetValueDisclosureService extends ServiceImpl<NetValueDisclosureMapper, NetValueDisclosure> {

    private ValuationDBMapper valuationDBMapper;
    private SecondaryValuationMapService secondaryValuationMapService;
    private RpaExecuteService rpaExecuteService;
    private CronService cronService;
    private AccountInformationService accountInformationService;
    private LogReconRecordsService logReconRecordsService;
    private LogMailSendRecordsService logMailSendRecordsService;
    private LogCustodianRecordsService logCustodianRecordsService;
    private LogValuationConfirmRecordsService logValuationConfirmRecordsService;
    private ValuationTableRecordsService valuationTableRecordsService;
    private InvestorContactsService investorContactsService;
    private NetValueDisclosureT1BatchService netValueDisclosureT1BatchService;

    @Autowired
    public void setValuationDBMapper(ValuationDBMapper valuationDBMapper) {
        this.valuationDBMapper = valuationDBMapper;
    }

    @Autowired
    public void setSecondaryValuationMapService(SecondaryValuationMapService secondaryValuationMapService) {
        this.secondaryValuationMapService = secondaryValuationMapService;
    }

    @Autowired
    public void setRpaExecuteService(RpaExecuteService rpaExecuteService) {
        this.rpaExecuteService = rpaExecuteService;
    }

    @Autowired
    public void setCronService(CronService cronService) {
        this.cronService = cronService;
    }

    @Autowired
    public void setAccountInformationService(AccountInformationService accountInformationService) {
        this.accountInformationService = accountInformationService;
    }

    @Autowired
    public void setLogReconRecordsService(LogReconRecordsService logReconRecordsService) {
        this.logReconRecordsService = logReconRecordsService;
    }

    @Autowired
    public void setLogMailSendRecordsService(LogMailSendRecordsService logMailSendRecordsService) {
        this.logMailSendRecordsService = logMailSendRecordsService;
    }

    @Autowired
    public void setLogCustodianRecordsService(LogCustodianRecordsService logCustodianRecordsService) {
        this.logCustodianRecordsService = logCustodianRecordsService;
    }

    @Autowired
    public void setLogValuationConfirmRecordsService(
            LogValuationConfirmRecordsService logValuationConfirmRecordsService) {
        this.logValuationConfirmRecordsService = logValuationConfirmRecordsService;
    }

    @Autowired
    public void setValuationTableRecordsService(ValuationTableRecordsService valuationTableRecordsService) {
        this.valuationTableRecordsService = valuationTableRecordsService;
    }

    @Autowired
    public void setInvestorContactsService(InvestorContactsService investorContactsService) {
        this.investorContactsService = investorContactsService;
    }

    @Autowired
    public void setNetValueDisclosureT1BatchService(NetValueDisclosureT1BatchService netValueDisclosureT1BatchService) {
        this.netValueDisclosureT1BatchService = netValueDisclosureT1BatchService;
    }

    public Map<String, Object> showRecords(String productId, String dataDate) {
        // 估值表生成结果记录
        LambdaQueryWrapper<ValuationTableRecords> valuationTableRecordsLambdaQueryWrapper = new LambdaQueryWrapper<>();
        valuationTableRecordsLambdaQueryWrapper.eq(ValuationTableRecords::getProductId, productId);
        valuationTableRecordsLambdaQueryWrapper.eq(ValuationTableRecords::getValuationDate, dataDate);
        List<ValuationTableRecords> valuationTableRecords = valuationTableRecordsService
                .list(valuationTableRecordsLambdaQueryWrapper);
        // 托管对账结果记录
        LambdaQueryWrapper<LogCustodianRecords> custodianRecordsLambdaQueryWrapper = new LambdaQueryWrapper<>();
        custodianRecordsLambdaQueryWrapper.eq(LogCustodianRecords::getProductId, productId);
        custodianRecordsLambdaQueryWrapper.eq(LogCustodianRecords::getValuationDate, dataDate);
        List<LogCustodianRecords> logCustodianRecords = logCustodianRecordsService
                .list(custodianRecordsLambdaQueryWrapper);
        // 估值表确认结果记录
        LambdaQueryWrapper<LogValuationConfirmRecords> valuationConfirmRecordsLambdaQueryWrapper = new LambdaQueryWrapper<>();
        valuationConfirmRecordsLambdaQueryWrapper.eq(LogValuationConfirmRecords::getProductId, productId);
        valuationConfirmRecordsLambdaQueryWrapper.eq(LogValuationConfirmRecords::getValuationDate, dataDate);
        List<LogValuationConfirmRecords> logValuationConfirmRecords = logValuationConfirmRecordsService
                .list(valuationConfirmRecordsLambdaQueryWrapper);
        // 估值表对账结果记录
        LambdaQueryWrapper<LogReconRecords> logReconRecordsLambdaQueryWrapper = new LambdaQueryWrapper<>();
        logReconRecordsLambdaQueryWrapper.eq(LogReconRecords::getProductId, productId);
        logReconRecordsLambdaQueryWrapper.eq(LogReconRecords::getValuationDate, dataDate);
        List<LogReconRecords> logReconRecords = logReconRecordsService.list(logReconRecordsLambdaQueryWrapper);
        // 披露状态
        LambdaQueryWrapper<LogMailSendRecords> logMailSendRecordsLambdaQueryWrapper = new LambdaQueryWrapper<>();
        logMailSendRecordsLambdaQueryWrapper.eq(LogMailSendRecords::getProductId, productId);
        logMailSendRecordsLambdaQueryWrapper.eq(LogMailSendRecords::getValuationDate, dataDate);
        List<LogMailSendRecords> logMailSendRecords = logMailSendRecordsService
                .list(logMailSendRecordsLambdaQueryWrapper);
        return Map.of("valuationTableRecords", valuationTableRecords,
                "logCustodianRecords", logCustodianRecords,
                "logReconRecords", logReconRecords,
                "logValuationConfirmRecords", logValuationConfirmRecords,
                "logMailSendRecords", logMailSendRecords);
    }

    public void startDownloadValuationJob(String dataDate, List<String> productIds, boolean isAgain) {
        log.info("开始手动下载估值表, 需要下载的数据ID为:{}, 日期为:{}, 是否再次下载情况:{}", productIds, dataDate, isAgain);
        List<String> jobIds = cronService.getJobIdByClass(SyncValuationTableJob.class);
        List<NetValueDisclosure> needDeal = getData(productIds, dataDate);
        JobDataMap jobDataMap = new JobDataMap();
        jobDataMap.put(DATA_DATE, dataDate);
        jobDataMap.put(NET_VALUE_DISCLOSURE_LIST, needDeal);
        jobDataMap.put(IS_AGAIN, isAgain);
        cronService.startJobNow(jobIds, jobDataMap);
    }

    public void startDownloadValuationJobSimple(String dataDate, @NonNull List<NetValueDisclosure> needDeal,
            boolean isAgain) {
        if (!needDeal.isEmpty()) {
            log.info("开始下载估值表, 需要下载的数据ID为:{}, 日期为:{}, 是否再次下载情况:{}",
                    needDeal.stream().map(NetValueDisclosure::getProductId).collect(Collectors.toList()), dataDate,
                    isAgain);

            // 检查是否有正在运行的RPA任务，避免重复下载
            List<NetValueDisclosure> filteredNeedDeal = needDeal.stream()
                    .filter(netValueDisclosure -> {
                        if (isAgain) {
                            // 二次下载时，检查二次下载状态
                            boolean isRunning = ValuationRPAStatus.RUNNING.name()
                                    .equals(netValueDisclosure.getRpaSecondStatus());
                            if (isRunning) {
                                log.info("产品ID: {} 的二次下载RPA任务正在运行，跳过", netValueDisclosure.getProductId());
                            }
                            return !isRunning;
                        } else {
                            // 首次下载时，检查首次下载状态
                            boolean isRunning = ValuationRPAStatus.RUNNING.name()
                                    .equals(netValueDisclosure.getRpaStatus());
                            if (isRunning) {
                                log.info("产品ID: {} 的首次下载RPA任务正在运行，跳过", netValueDisclosure.getProductId());
                            }
                            return !isRunning;
                        }
                    })
                    .collect(Collectors.toList());

            if (filteredNeedDeal.isEmpty()) {
                log.info("过滤后没有需要下载的估值表");
                return;
            }

            log.info("过滤后需要下载的估值表数量: {}", filteredNeedDeal.size());
            List<String> jobIds = cronService.getJobIdByClass(SyncValuationTableJob.class);
            JobDataMap jobDataMap = new JobDataMap();
            jobDataMap.put(DATA_DATE, dataDate);
            jobDataMap.put(NET_VALUE_DISCLOSURE_LIST, filteredNeedDeal);
            jobDataMap.put(IS_AGAIN, isAgain);
            jobDataMap.put(HANDLER_TYPE, VALUATION_TABLE); // 确保设置了处理器类型
            cronService.startJobNow(jobIds, jobDataMap);
        }
    }

    public void startDownloadBalanceJob(String dataDate, List<String> productIds) {
        log.info("开始手动下载余额表, 需要下载的数据ID为:{}, 日期为:{}", productIds, dataDate);
        List<String> jobIds = cronService.getJobIdByClass(SyncBalanceTableJob.class);
        List<NetValueDisclosure> needDeal = getData(productIds, dataDate);
        JobDataMap jobDataMap = new JobDataMap();
        jobDataMap.put(DATA_DATE, dataDate);
        jobDataMap.put(NET_VALUE_DISCLOSURE_LIST, needDeal);
        cronService.startJobNow(jobIds, jobDataMap);
    }

    public void startDownloadBalanceJobSimple(String dataDate, @NonNull List<NetValueDisclosure> needDeal) {
        if (!needDeal.isEmpty()) {
            log.info("开始下载余额表, 需要下载的数据ID为:{}, 日期为:{}",
                    needDeal.stream().map(NetValueDisclosure::getProductId).collect(Collectors.toList()), dataDate);

            // 检查是否有正在运行的RPA任务，避免重复下载
            List<NetValueDisclosure> filteredNeedDeal = needDeal.stream()
                    .filter(netValueDisclosure -> {
                        boolean isRunning = ValuationRPAStatus.RUNNING.name()
                                .equals(netValueDisclosure.getBalanceRpaStatus());
                        if (isRunning) {
                            log.info("产品ID: {} 的余额表下载RPA任务正在运行，跳过", netValueDisclosure.getProductId());
                        }
                        return !isRunning;
                    })
                    .collect(Collectors.toList());

            if (filteredNeedDeal.isEmpty()) {
                log.info("过滤后没有需要下载的余额表");
                return;
            }

            log.info("过滤后需要下载的余额表数量: {}", filteredNeedDeal.size());
            List<String> jobIds = cronService.getJobIdByClass(SyncBalanceTableJob.class);
            JobDataMap jobDataMap = new JobDataMap();
            jobDataMap.put(DATA_DATE, dataDate);
            jobDataMap.put(NET_VALUE_DISCLOSURE_LIST, filteredNeedDeal);
            jobDataMap.put(HANDLER_TYPE, BALANCE_TABLE); // 确保设置了处理器类型
            cronService.startJobNow(jobIds, jobDataMap);
        }
    }

    /**
     * 修改对账状态
     */
    public void modifyReconStatus(List<String> productIds, String dataDate, String reconStatus, String reconReason) {
        if (productIds != null && !productIds.isEmpty()) {
            LambdaUpdateWrapper<NetValueDisclosure> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.in(NetValueDisclosure::getProductId, productIds);
            updateWrapper.eq(NetValueDisclosure::getValuationDate, dataDate);
            updateWrapper.set(NetValueDisclosure::getReconciliationStatus, reconStatus);
            updateWrapper.set(NetValueDisclosure::getManualReconConfirm, 1);
            this.update(updateWrapper);
            List<LogReconRecords> logReconRecords = composeRecon(productIds, dataDate, reconStatus, reconReason);
            logReconRecordsService.saveBatch(logReconRecords);
        }
    }

    private List<LogReconRecords> composeRecon(List<String> productIds, String dataDate, String reconStatus,
            String reconReason) {
        List<LogReconRecords> logReconRecords = new ArrayList<>();
        for (String productId : productIds) {
            LogReconRecords logReconRecord = new LogReconRecords();
            logReconRecord.setProductId(productId);
            logReconRecord.setValuationDate(dataDate);
            logReconRecord.setOperateReason(reconReason);
            logReconRecord.setReconStatus(reconStatus);
            logReconRecord.setReconDate(new Date());
            logReconRecord.setReconOperator(DEFAULT_USERNAME);
            logReconRecords.add(logReconRecord);
        }
        return logReconRecords;
    }

    public void sendNetValueDisclosure(List<String> productIds, String dataDate) {
        List<String> jobIds = cronService.getJobIdByClass(NetValueDisclosureMailSendJob.class);
        List<NetValueDisclosure> dataList = getData(productIds, dataDate);
        JobDataMap jobDataMap = new JobDataMap();
        jobDataMap.put(DATA_DATE, dataDate);
        jobDataMap.put(PRODUCT_ID, productIds);
        jobDataMap.put(REMOTE_FILE, new LinkedHashMap<String, RemoteFileInfo>());
        jobDataMap.put(NET_VALUE_DISCLOSURE_LIST, dataList);
        jobDataMap.put(SYNC, true);
        cronService.startJobNow(jobIds, jobDataMap);
    }

    public void sendNetValueDisclosureSimple(List<NetValueDisclosure> dataList, String dataDate) {
        if (!dataList.isEmpty()) {
            // 使用乐观锁机制，逐个尝试锁定记录
            List<NetValueDisclosure> lockedRecords = new ArrayList<>();
            List<String> productIds = dataList.stream().map(NetValueDisclosure::getProductId).collect(Collectors.toList());

            for (String productId : productIds) {
                // 使用乐观锁尝试将状态从0更新为2（表示正在处理中）
                LambdaUpdateWrapper<NetValueDisclosure> updateWrapper = new LambdaUpdateWrapper<>();
                updateWrapper.eq(NetValueDisclosure::getValuationDate, dataDate);
                updateWrapper.eq(NetValueDisclosure::getProductId, productId);
                updateWrapper.eq(NetValueDisclosure::getNetValueDisclosed, 0); // 乐观锁条件
                updateWrapper.set(NetValueDisclosure::getNetValueDisclosed, 2); // 设置为处理中状态（2表示正在发送中）

                boolean updated = this.update(updateWrapper);
                if (updated) {
                    // 更新成功，从原始数据中找到对应记录并更新其状态
                    NetValueDisclosure originalRecord = dataList.stream()
                            .filter(record -> productId.equals(record.getProductId()))
                            .findFirst()
                            .orElse(null);

                    if (originalRecord != null) {
                        // 直接更新原始记录的状态为锁定状态
                        originalRecord.setNetValueDisclosed(2);
                        lockedRecords.add(originalRecord);
                        log.debug("成功锁定净值披露记录: {}", productId);
                    }
                } else {
                    log.debug("净值披露记录已被其他进程处理: {}", productId);
                }
            }

            if (lockedRecords.isEmpty()) {
                log.info("没有成功锁定的净值披露记录，可能都已被其他进程处理");
                return;
            }

            log.info("开始发送披露邮件, 账套编号为:{}, 日期为:{}",
                    lockedRecords.stream().map(NetValueDisclosure::getProductId).collect(Collectors.toList()), dataDate);

            List<String> jobIds = cronService.getJobIdByClass(NetValueDisclosureMailSendJob.class);
            JobDataMap jobDataMap = new JobDataMap();
            jobDataMap.put(DATA_DATE, dataDate);
            jobDataMap.put(REMOTE_FILE, new LinkedHashMap<String, RemoteFileInfo>());
            jobDataMap.put(NET_VALUE_DISCLOSURE_LIST, lockedRecords);
            jobDataMap.put(SYNC, true);
            List<String> lockedProductIds = lockedRecords.stream().map(NetValueDisclosure::getProductId)
                    .collect(Collectors.toList());
            jobDataMap.put(PRODUCT_ID, lockedProductIds);
            cronService.startJobNow(jobIds, jobDataMap);
        }
    }

    public void sendCustodianBankMail(List<String> productIds, String dataDate) {
        List<String> jobIds = cronService.getJobIdByClass(CustodianBankMailSendJob.class);
        doSendMail(productIds, dataDate, jobIds, true);
    }

    public void sendInvestorMail(List<String> productIds, String dataDate) {
        List<String> jobIds = cronService.getJobIdByClass(InvestorMailSendJob.class);
        doSendMail(productIds, dataDate, jobIds, false);
    }

    private Set<String> getSZTProductIds(@NonNull List<String> productIds) {
        LambdaQueryWrapper<InvestorContacts> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(InvestorContacts::getMethod, DisclosureMethod.SZT.name());
        queryWrapper.ne(InvestorContacts::getHandler, MailContentHandler.THIRD_NET_VALUE.name());
        if (!productIds.isEmpty()) {
            queryWrapper.in(InvestorContacts::getProductId, productIds);
        }
        List<InvestorContacts> list = investorContactsService.list(queryWrapper);
        return list.stream().map(InvestorContacts::getProductId).distinct().collect(Collectors.toSet());
    }

    private Map<String, Set<InvestorContacts>> getThirdSZTContacts(@NonNull List<String> productIds) {
        LambdaQueryWrapper<InvestorContacts> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(InvestorContacts::getMethod, DisclosureMethod.SZT.name());
        queryWrapper.eq(InvestorContacts::getHandler, MailContentHandler.THIRD_NET_VALUE.name());
        if (!productIds.isEmpty()) {
            queryWrapper.in(InvestorContacts::getProductId, productIds);
        }
        List<InvestorContacts> list = investorContactsService.list(queryWrapper);
        return list.stream().collect(Collectors.groupingBy(InvestorContacts::getProductId, Collectors.toSet()));
    }

    private Map<String, String> getSZTProductPath(@NonNull Set<String> productIds) {
        if (productIds.isEmpty()) {
            return Map.of();
        }
        LambdaQueryWrapper<InvestorContacts> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(InvestorContacts::getMethod, DisclosureMethod.SZT.name());
        queryWrapper.in(InvestorContacts::getProductId, productIds);
        List<InvestorContacts> list = investorContactsService.list(queryWrapper);
        return list.stream()
                .collect(Collectors.toMap(InvestorContacts::getProductId,
                        investorContacts -> investorContacts.getSztPath() == null ? "" : investorContacts.getSztPath(),
                        (path1, path2) -> path2));
    }

    private Map<String, String> getThirdSZTProductPath(@NonNull Map<String, Set<InvestorContacts>> thirdSZTContacts) {
        if (thirdSZTContacts.isEmpty()) {
            return Map.of();
        }
        return thirdSZTContacts.entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, entry -> {
            Set<InvestorContacts> investorContacts = entry.getValue();
            return investorContacts.stream().map(InvestorContacts::getSztPath).filter(Objects::nonNull).distinct()
                    .collect(Collectors.joining(","));
        }));
    }

    public void sendToSZT(List<String> productIds, String dataDate) {
        // 筛选需要发深证通的账套
        Set<String> sztProductIds = getSZTProductIds(productIds);
        if (sztProductIds.isEmpty()) {
            log.info("没有需要发送深证通的账号");
            return;
        }
        Map<String, String> sztProductPath = getSZTProductPath(sztProductIds);
        log.info("发深证通的账套:{}", sztProductIds);
        log.info("发深证通的账套及路径:{}", sztProductPath);
        List<String> jobIds = cronService.getJobIdByClass(SendSZTJob.class);
        List<NetValueDisclosure> needDeal = getData(new ArrayList<>(sztProductIds), dataDate);
        JobDataMap jobDataMap = new JobDataMap();
        jobDataMap.put(DATA_DATE, dataDate);
        jobDataMap.put(NET_VALUE_DISCLOSURE_LIST, needDeal);
        jobDataMap.put(SZT_PATH, sztProductPath);
        cronService.startJobNow(jobIds, jobDataMap);
    }

    public void sendToSZTThird(@NonNull List<String> productIds, @NonNull String dataDate) {
        // 参数校验
        if (productIds.isEmpty()) {
            log.error("产品ID列表为空");
            return;
        }

        // 筛选需要发深证通的账套
        Map<String, Set<InvestorContacts>> thirdSZTContacts = getThirdSZTContacts(productIds);
        if (thirdSZTContacts.isEmpty()) {
            log.error("没有需要发送第三方深证通的账号");
            return;
        }

        // 获取深证通路径
        Map<String, String> sztProductPath = getThirdSZTProductPath(thirdSZTContacts);
        if (sztProductPath.isEmpty()) {
            log.error("深证通路径为空");
            return;
        }

        // 获取第三方净值披露映射 (投资人 -> 产品ID列表)
        Map<String, List<String>> thirdNetValueDisclosureMap = getThirdNetValueDisclosure(thirdSZTContacts);
        if (thirdNetValueDisclosureMap.isEmpty()) {
            log.error("第三方净值披露映射为空");
            return;
        }

        log.info("第三方深证通发送: 投资人数量={}, 投资人列表={}",
                thirdNetValueDisclosureMap.size(), thirdNetValueDisclosureMap.keySet());

        // 获取定时任务ID
        List<String> jobIds = cronService.getJobIdByClass(SendThirdSZTJob.class);
        if (jobIds.isEmpty()) {
            log.error("未找到SendSZTJob定时任务");
            return;
        }

        // 获取需要处理的数据 - 现在按投资人分组
        Map<String, List<NetValueDisclosure>> needDeal = thirdNetValueDisclosureMap.entrySet()
                .stream()
                .collect(Collectors.toMap(
                        Map.Entry::getKey, // 投资人名称
                        entry -> getData(entry.getValue(), dataDate) // 该投资人的产品净值披露数据
                ));

        // 构建任务数据
        JobDataMap jobDataMap = new JobDataMap();
        jobDataMap.put(DATA_DATE, dataDate);
        jobDataMap.put(NET_VALUE_DISCLOSURE_THIRD_MAP, needDeal);
        jobDataMap.put(SZT_PATH, sztProductPath);

        // 启动定时任务
        cronService.startJobNow(jobIds, jobDataMap);
    }

    private Map<String, List<String>> getThirdNetValueDisclosure(Map<String, Set<InvestorContacts>> thirdSZTContacts) {
        Map<String, List<String>> result = new HashMap<>();
        for (Map.Entry<String, Set<InvestorContacts>> entry : thirdSZTContacts.entrySet()) {
            Set<InvestorContacts> investorContacts = entry.getValue();
            for (InvestorContacts contact : investorContacts) {
                String investor = contact.getInvestor();
                String productId = contact.getProductId();
                result.computeIfAbsent(investor, k -> new ArrayList<>()).add(productId);
            }
        }
        return result;
    }

    public void sendInvestorStatementMail(List<String> productIds, String dataDate) {
        List<String> jobIds = cronService.getJobIdByClass(InvestorStatementMailSendJob.class);
        doSendMail(productIds, dataDate, jobIds, false);
    }

    public void sendThirdMail(List<String> productIds, String dataDate) {
        List<String> jobIds = cronService.getJobIdByClass(ThirdMailSendJob.class);
        doSendMail(productIds, dataDate, jobIds, false);
    }

    private void doSendMail(List<String> productIds, String dataDate, List<String> jobIds, boolean hasBalance) {
        List<NetValueDisclosure> dataList = getData(productIds, dataDate);
        JobDataMap jobDataMap = new JobDataMap();
        jobDataMap.put(DATA_DATE, dataDate);
        jobDataMap.put(PRODUCT_ID, productIds);
        Map<String, RemoteFileInfo> remoteFileInfoMap = getFileByNetValueData(dataList, hasBalance);
        jobDataMap.put(REMOTE_FILE, remoteFileInfoMap);
        jobDataMap.put(NET_VALUE_DISCLOSURE_LIST, dataList);
        jobDataMap.put(SYNC, true);
        cronService.startJobNow(jobIds, jobDataMap);
    }

    private void doSendMailSimple(List<NetValueDisclosure> dataList, String dataDate, List<String> jobIds,
            boolean hasBalance) {
        if (!dataList.isEmpty()) {
            log.info("开始发送邮件, 账套编号为:{}, 日期为:{}",
                    dataList.stream().map(NetValueDisclosure::getProductId).collect(Collectors.toList()), dataDate);
            log.info("邮件进程开始前数据状态:{}", dataList);
            JobDataMap jobDataMap = new JobDataMap();
            jobDataMap.put(DATA_DATE, dataDate);
            Map<String, RemoteFileInfo> remoteFileInfoMap = getFileByNetValueData(dataList, hasBalance);
            jobDataMap.put(REMOTE_FILE, remoteFileInfoMap);
            jobDataMap.put(NET_VALUE_DISCLOSURE_LIST, dataList);
            jobDataMap.put(SYNC, true);
            List<String> productIds = dataList.stream().map(NetValueDisclosure::getProductId)
                    .collect(Collectors.toList());
            jobDataMap.put(PRODUCT_ID, productIds);
            cronService.startJobNow(jobIds, jobDataMap);
        }
    }

    private Map<String, RemoteFileInfo> getFileByNetValueData(List<NetValueDisclosure> dataList, boolean hasBalance) {
        Objects.requireNonNull(dataList, "数据不得为空");
        Map<String, RemoteFileInfo> resultMap = new LinkedHashMap<>();
        for (NetValueDisclosure netValueDisclosure : dataList) {
            String valuationTablePath = netValueDisclosure.getValuationTablePath();
            composeFile(netValueDisclosure, valuationTablePath, resultMap);
            if (hasBalance) {
                String balanceTablePath = netValueDisclosure.getBalanceTablePath();
                composeFile(netValueDisclosure, balanceTablePath, resultMap);
            }
        }
        return resultMap;
    }

    private void composeFile(NetValueDisclosure netValueDisclosure, String valuationTablePath,
            Map<String, RemoteFileInfo> resultMap) {
        if (valuationTablePath != null) {
            String[] split = valuationTablePath.split("\\\\");
            if (split.length > 1) {
                String fileName = split[split.length - 1];
                String[] pathArr = new String[split.length - 1];
                System.arraycopy(split, 0, pathArr, 0, split.length - 1);
                String path = String.join("\\", pathArr) + "\\";
                RemoteFileInfo remoteFileInfo = new RemoteFileInfo();
                remoteFileInfo.setFileName(fileName);
                remoteFileInfo.setRelativePath(path);
                resultMap.put(netValueDisclosure.getProductId(), remoteFileInfo);
            }
        }
    }

    /**
     * 同步生成后同步估值表 手动同步接口
     *
     * @param list    当日净值披露数据
     * @param context 定时任务上下文,保证任务是通过定时任务调用的
     */
    public void doSyncValuationTable(String dataDate, List<NetValueDisclosure> list, JobExecutionContext context) {
        if (context == null) {
            return;
        }
        // 如果RPA正在运行这个产品，则将其移除
        list = list.stream()
                .filter(netValueDisclosure -> (!ValuationRPAStatus.RUNNING.name()
                        .equals(netValueDisclosure.getRpaStatus())
                        && !ValuationRPAStatus.RUNNING.name().equals(netValueDisclosure.getRpaSecondStatus())))
                .collect(Collectors.toList());
        if (list.isEmpty()) {
            JobDataMap mergedJobDataMap = context.getMergedJobDataMap();
            mergedJobDataMap.put(SYNC, true);
            return;
        }
        doExecuteRPA(context, dataDate, list);
    }

    public void doSyncBalanceTable(String dataDate, List<NetValueDisclosure> list, JobExecutionContext context) {
        if (context == null) {
            return;
        }
        // 如果RPA正在运行这个产品，则将其移除
        list = list.stream()
                .filter(netValueDisclosure -> !ValuationRPAStatus.RUNNING.name()
                        .equals(netValueDisclosure.getBalanceRpaStatus()))
                .collect(Collectors.toList());
        if (list.isEmpty()) {
            JobDataMap mergedJobDataMap = context.getMergedJobDataMap();
            mergedJobDataMap.put(SYNC, true);
            return;
        }
        doExecuteRPA(context, dataDate, list);
    }

    public void doSendSZT(String dataDate, List<NetValueDisclosure> list, JobExecutionContext context) {
        if (context == null) {
            return;
        }
        // 如果RPA正在运行这个产品，则将其移除
        list = list.stream()
                .filter(netValueDisclosure -> !ValuationRPAStatus.RUNNING.name()
                        .equals(netValueDisclosure.getSztStatus()))
                .collect(Collectors.toList());
        if (list.isEmpty()) {
            JobDataMap mergedJobDataMap = context.getMergedJobDataMap();
            mergedJobDataMap.put(SYNC, true);
            return;
        }
        doExecuteSZT(context, dataDate, list);
    }

    /**
     * 查询并更新生成状态
     *
     * @param dataDate 估值数据日期
     * @param needDeal 需要处理生成状态的净值状态数据
     */
    public void doValuationTableGenerate(String dataDate, List<NetValueDisclosure> needDeal) {
        if (needDeal != null && !needDeal.isEmpty()) {
            List<ValuationTableStatus> generateStatus = valuationDBMapper.getGenerateStatus(dataDate);
            Map<String, Integer> valuationGenerate = generateStatus.stream().collect(
                    Collectors.toMap(ValuationTableStatus::getProductId, ValuationTableStatus::getGenerateStatus));
            for (NetValueDisclosure netValueDisclosure : needDeal) {
                String productId = netValueDisclosure.getProductId();
                Integer status = valuationGenerate.get(productId);
                if (status != null) {
                    netValueDisclosure.setValuationTableGenerated(status);
                }
            }
        }
    }

    /**
     * 查询并更新确认状态
     *
     * @param dataDate 估值数据日期
     * @param needDeal 需要处理生成状态的净值状态数据
     */
    public void doValuationTableConfirm(String dataDate, List<NetValueDisclosure> needDeal) {
        if (needDeal != null && !needDeal.isEmpty()) {
            List<ValuationTableStatus> confirmStatus = valuationDBMapper.getConfirmStatus(dataDate);
            Map<String, ValuationTableStatus> valuationConfirm = confirmStatus.stream()
                    .collect(Collectors.toMap(ValuationTableStatus::getProductId,
                            valuationTableStatus -> valuationTableStatus));
            List<LogValuationConfirmRecords> logValuationConfirmRecordsList = new ArrayList<>();
            needDeal.stream().filter(this::needConfirm).forEach(netValueDisclosureT0 -> {
                String productId = netValueDisclosureT0.getProductId();
                ValuationTableStatus status = valuationConfirm.get(productId);
                if (status != null) {
                    netValueDisclosureT0.setValuationTableConfirmed(status.getConfirmStatus());
                    LogValuationConfirmRecords logValuationConfirmRecords = composeConfirm(status,
                            netValueDisclosureT0);
                    logValuationConfirmRecordsList.add(logValuationConfirmRecords);
                }
            });
            if (!logValuationConfirmRecordsList.isEmpty()) {
                List<String> productIds = logValuationConfirmRecordsList.stream()
                        .map(LogValuationConfirmRecords::getProductId).collect(Collectors.toList());
                LambdaQueryWrapper<LogValuationConfirmRecords> logValuationConfirmRecordsLambdaQueryWrapper = new LambdaQueryWrapper<>();
                logValuationConfirmRecordsLambdaQueryWrapper.eq(LogValuationConfirmRecords::getValuationDate, dataDate);
                logValuationConfirmRecordsLambdaQueryWrapper.in(LogValuationConfirmRecords::getProductId, productIds);
                logValuationConfirmRecordsService.remove(logValuationConfirmRecordsLambdaQueryWrapper);
                logValuationConfirmRecordsService.saveBatch(logValuationConfirmRecordsList);
            }
        }
    }

    private void dealSecondary(String dataDate, List<NetValueDisclosure> needDeal, String valuationTime) {
        Objects.requireNonNull(needDeal, "needDeal不得为空");
        LambdaQueryWrapper<SecondaryValuationMap> secondaryValuationMapLambdaQueryWrapper = new LambdaQueryWrapper<>();
        secondaryValuationMapLambdaQueryWrapper.eq(SecondaryValuationMap::getValuationTime, valuationTime);
        List<SecondaryValuationMap> list = secondaryValuationMapService.list(secondaryValuationMapLambdaQueryWrapper);
        Map<String, String> subjectCodeMap = list.stream()
                .collect(Collectors.toMap(SecondaryValuationMap::getSubjectCode,
                        SecondaryValuationMap::getSecondaryName,
                        (oldOne, newOne) -> newOne));
        List<SecondValuationData> secondValuationDataList = valuationDBMapper
                .getSecondValuationData(subjectCodeMap.keySet(), dataDate, valuationTime);
        Map<String, List<SecondValuationData>> valuationDataMap = secondValuationDataList.stream().collect(Collectors
                .groupingBy(SecondValuationData::getProductId));
        for (NetValueDisclosure netValueDisclosure : needDeal) {
            String productId = netValueDisclosure.getProductId();
            List<SecondValuationData> secondValuationData = valuationDataMap.get(productId);
            if (secondValuationData != null) {
                if (ValuationTime.T0.name().equals(valuationTime)) {
                    Set<String> strSet = secondValuationData.stream()
                            .map(data -> subjectCodeMap.get(data.getSubjectCode()))
                            .collect(Collectors.toCollection(LinkedHashSet::new));
                    netValueDisclosure.setAssetType(String.join(",", strSet));
                    netValueDisclosure.setIsRevalued(1);
                } else {
                    Map<String, List<SecondValuationData>> result = new HashMap<>();
                    for (String subjectCode : subjectCodeMap.keySet()) {
                        for (SecondValuationData secondValuationDatum : secondValuationData) {
                            String subjectCodeInDb = secondValuationDatum.getSubjectCode();
                            if (subjectCodeInDb.startsWith(subjectCode)) {
                                List<SecondValuationData> subData = result.get(subjectCode);
                                if (subData == null) {
                                    subData = new ArrayList<>();
                                }
                                subData.add(secondValuationDatum);
                                result.put(subjectCode, subData);
                            }
                        }
                    }
                    Set<String> resultList = new LinkedHashSet<>();
                    for (Map.Entry<String, List<SecondValuationData>> entry : result.entrySet()) {
                        String key = entry.getKey();
                        List<SecondValuationData> value = entry.getValue();
                        if (value != null) {
                            for (SecondValuationData valuationData : value) {
                                String subjectName = valuationData.getSubjectName();
                                if (subjectName.startsWith(T1_SECOND_VALUATION)) {
                                    String secondaryValuationName = subjectCodeMap.get(key);
                                    if (secondaryValuationName != null) {
                                        resultList.add(secondaryValuationName);
                                        break;
                                    }
                                }
                            }
                        }
                    }
                    if (!resultList.isEmpty()) {
                        netValueDisclosure.setAssetType(String.join("，", resultList));
                        netValueDisclosure.setIsRevalued(1);
                    }
                }
            }
        }
    }

    private LogValuationConfirmRecords composeConfirm(ValuationTableStatus valuationTableStatus,
            NetValueDisclosure netValueDisclosure) {
        LogValuationConfirmRecords logValuationConfirmRecords = new LogValuationConfirmRecords();
        logValuationConfirmRecords.setValuationTablePath(netValueDisclosure.getValuationTablePath());
        logValuationConfirmRecords.setConfirmStatus(Integer.toString(valuationTableStatus.getConfirmStatus()));
        logValuationConfirmRecords.setConfirmTime(valuationTableStatus.getConfirmTime());
        logValuationConfirmRecords.setConfirmOperator(DEFAULT_USERNAME);
        logValuationConfirmRecords.setValuationDate(netValueDisclosure.getValuationDate());
        logValuationConfirmRecords.setProductId(netValueDisclosure.getProductId());
        return logValuationConfirmRecords;
    }

    private boolean needConfirm(NetValueDisclosure netValueDisclosure) {
        if (netValueDisclosure == null) {
            return false;
        }
        // 生成状态为已生成
        Integer valuationTableGenerated = netValueDisclosure.getValuationTableGenerated();
        // 托管对账邮件发送 无需发送或者已发送
        String custodyReconciliationEmailSent = netValueDisclosure.getCustodyReconciliationEmailSent();
        MailStatus mailStatus = MailStatus.valueOf(custodyReconciliationEmailSent);
        // 估值表对账状态为已完成
        String reconciliationStatus = netValueDisclosure.getReconciliationStatus();
        ValuationStatus valuationStatus = ValuationStatus.valueOf(reconciliationStatus);
        return valuationTableGenerated == 1 && (MailStatus.NONE == mailStatus || MailStatus.SUCCESS == mailStatus)
                && ValuationStatus.COMPLETED == valuationStatus;
    }

    public List<NetValueDisclosure> getData(List<String> productIds, String dataDate) {
        Objects.requireNonNull(dataDate, "数据日期不得为空");
        LambdaQueryWrapper<NetValueDisclosure> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(NetValueDisclosure::getValuationDate, dataDate);
        if (productIds != null && !productIds.isEmpty()) {
            queryWrapper.in(NetValueDisclosure::getProductId, productIds);
        }
        return this.list(queryWrapper);
    }

    /**
     * 执行RPA
     *
     * @param context  job上下文
     * @param dataDate 数据日期
     * @param needDeal 需要处理的净值集合
     */
    private void doExecuteRPA(JobExecutionContext context, String dataDate, List<NetValueDisclosure> needDeal) {
        if (context == null) {
            return;
        }
        if (!needDeal.isEmpty()) {
            JobDataMap mergedJobDataMap = context.getMergedJobDataMap();
            CronInfo cronInfo = cronService.getCronInfo(mergedJobDataMap);
            FlowList flow = cronInfo.getFlow();
            String logId = cronInfo.getLogId();
            boolean isAgain = mergedJobDataMap.getBooleanValue(IS_AGAIN);
            String productIdStr = needDeal.stream().map(NetValueDisclosure::getProductId)
                    .collect(Collectors.joining(","));
            Map<String, Object> flowExtendParams = buildFlowExtendParams(dataDate, productIdStr);
            RpaExecLog rpaExecLog = rpaExecuteService.executeRPA(flow, flowExtendParams, logId);
            String handlerType = (String) mergedJobDataMap.get(HANDLER_TYPE);
            updateRPAStatus(needDeal, handlerType, isAgain);
            Map<String, Object> param = new HashMap<>();
            param.put(NET_VALUE_DISCLOSURE_LIST, needDeal);
            param.put(IS_AGAIN, isAgain);
            param.put(DATA_DATE, dataDate);
            rpaExecuteService.startTimer(rpaExecLog, flow, param, logId);
        }
    }

    @SuppressWarnings("unchecked")
    private void doExecuteSZT(JobExecutionContext context, String dataDate, List<NetValueDisclosure> needDeal) {
        if (context == null) {
            return;
        }
        if (!needDeal.isEmpty()) {
            JobDataMap mergedJobDataMap = context.getMergedJobDataMap();
            CronInfo cronInfo = cronService.getCronInfo(mergedJobDataMap);
            FlowList flow = cronInfo.getFlow();
            String logId = cronInfo.getLogId();
            Map<String, String> idNameMap = accountInformationService.list().stream()
                    .collect(Collectors.toMap(AccountInformation::getId,
                            AccountInformation::getFullProductName,
                            (oldOne, newOne) -> newOne));
            StringBuilder builder = new StringBuilder();
            Map<String, String> sztProductPath = (Map<String, String>) mergedJobDataMap.get(SZT_PATH);
            Set<Map.Entry<String, String>> entries = sztProductPath.entrySet();
            for (Map.Entry<String, String> entry : entries) {
                String productId = entry.getKey();
                String sztPath = entry.getValue();
                String productName = idNameMap.get(productId);
                builder.append(productName).append("|").append(sztPath).append(",");
            }
            String productIdStr = builder.toString();
            if (productIdStr.endsWith(",")) {
                productIdStr = productIdStr.substring(0, productIdStr.length() - 1);
            }
            log.info("发送深证通数据名称:{}", productIdStr);
            Map<String, Object> flowExtendParams = buildSZTFlowExtendParams(dataDate, productIdStr);
            RpaExecLog rpaExecLog = rpaExecuteService.executeRPA(flow, flowExtendParams, logId);
            Map<String, Object> param = new HashMap<>();
            param.put(NET_VALUE_DISCLOSURE_LIST, needDeal);
            rpaExecuteService.startTimer(rpaExecLog, flow, param, logId);
        }
    }

    private Map<String, Object> buildFlowExtendParams(String dataDate, String productIdStr) {
        return Map.of("数据日期", dataDate, "账套编码", productIdStr);
    }

    private Map<String, Object> buildSZTFlowExtendParams(String dataDate, String productNameStr) {
        return Map.of("数据日期", dataDate, "数据名称", productNameStr);
    }

    /**
     * 更新运行状态
     *
     * @param hasGenerated 已经生产估值表的净值数据 用于更新
     */
    private void updateRPAStatus(List<NetValueDisclosure> hasGenerated, String handlerType, boolean isAgain) {
        if (hasGenerated == null || hasGenerated.isEmpty()) {
            return;
        }
        List<String> ids = hasGenerated.stream().map(NetValueDisclosure::getId).collect(Collectors.toList());
        log.info("更新如下产品{}的{}状态", ids,handlerType);
        LambdaUpdateWrapper<NetValueDisclosure> netValueDisclosureT0LambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        if (!ids.isEmpty()) {
            netValueDisclosureT0LambdaUpdateWrapper.in(NetValueDisclosure::getId, ids);
            if (VALUATION_TABLE.equals(handlerType)) {
                if (isAgain) {
                    log.info("设置rpaSecondStatus为RUNNING");
                    netValueDisclosureT0LambdaUpdateWrapper.set(NetValueDisclosure::getRpaSecondStatus,
                            ValuationRPAStatus.RUNNING.name());
                    netValueDisclosureT0LambdaUpdateWrapper.set(NetValueDisclosure::getValuationTableDownloaded,
                            ValuationDownloadStatus.DOWNLOADING.name());
                    hasGenerated.forEach(netValueDisclosureT0 -> netValueDisclosureT0
                            .setRpaSecondStatus(ValuationRPAStatus.RUNNING.name()));
                } else {
                    log.info("设置getRpaStatus为RUNNING");
                    netValueDisclosureT0LambdaUpdateWrapper.set(NetValueDisclosure::getRpaStatus,
                            ValuationRPAStatus.RUNNING.name());
                    hasGenerated.forEach(netValueDisclosureT0 -> netValueDisclosureT0
                            .setRpaStatus(ValuationRPAStatus.RUNNING.name()));
                }
                this.update(netValueDisclosureT0LambdaUpdateWrapper);
            } else if (BALANCE_TABLE.equals(handlerType)) {
                log.info("设置getBalanceRpaStatus为RUNNING");
                netValueDisclosureT0LambdaUpdateWrapper.set(NetValueDisclosure::getBalanceRpaStatus,
                        ValuationRPAStatus.RUNNING.name());
                hasGenerated.forEach(netValueDisclosureT0 -> netValueDisclosureT0
                        .setBalanceRpaStatus(ValuationRPAStatus.RUNNING.name()));
                this.update(netValueDisclosureT0LambdaUpdateWrapper);
            } else if (DBF_FILE.equals(handlerType)) {
                log.info("设置getDbfStatus为RUNNING");
                netValueDisclosureT0LambdaUpdateWrapper.set(NetValueDisclosure::getDbfStatus,
                        ValuationRPAStatus.RUNNING.name());
                hasGenerated.forEach(
                        netValueDisclosureT0 -> netValueDisclosureT0.setDbfStatus(ValuationRPAStatus.RUNNING.name()));
                this.update(netValueDisclosureT0LambdaUpdateWrapper);
            }
        }
    }

    /**
     * 获取净值信息
     *
     * @param dataDate   估值日期
     * @param productIds 需要获取的账套ID
     * @return 净值信息
     */
    public List<ValuationTableData> getNetValueFromValuation(String dataDate, List<String> productIds) {
        List<ValuationTableData> resultData = new ArrayList<>();
        if (dataDate == null || dataDate.isEmpty()) {
            return resultData;
        }
        List<AccountInformation> accountInformationList = accountInformationService.list();
        Map<String, Integer> isStructuredMap = accountInformationList.stream()
                .collect(Collectors.toMap(AccountInformation::getId, AccountInformation::getIsStructured));
        Map<Integer, List<String>> groupProductIdMap = productIds.stream()
                .collect(Collectors.groupingBy(isStructuredMap::get));
        List<String> flatProductIds = groupProductIdMap.get(0);
        if (flatProductIds != null && !flatProductIds.isEmpty()) {
            resultData.addAll(dealTableData(valuationDBMapper.getFlatTableData(dataDate, flatProductIds)));
        }
        List<String> structuredProductIds = groupProductIdMap.get(1);
        if (structuredProductIds != null && !structuredProductIds.isEmpty()) {
            resultData.addAll(dealTableData(valuationDBMapper.getStructuralTableData(dataDate, structuredProductIds)));
        }
        return resultData;
    }

    /**
     * 处理整体净值数据 根据字段分为平层以及结构化
     *
     * @param tableData 待处理数据集
     */
    private List<ValuationTableData> dealTableData(List<ValuationTableData> tableData) {
        if (tableData == null) {
            return new ArrayList<>();
        }
        List<AccountInformation> accountInformationList = accountInformationService.list();
        Map<String, AccountInformation> idMap = accountInformationList.stream()
                .collect(Collectors.toMap(AccountInformation::getId,
                        accountInformation -> accountInformation,
                        (oldInfo, newInfo) -> newInfo));
        tableData.forEach(netValueDisclosureT0 -> {
            String productType = netValueDisclosureT0.getProductType();
            switch (productType) {
                case PRODUCT_TYPE_FLAT:
                    dealFlatValue(netValueDisclosureT0, idMap);
                    break;
                case PRODUCT_TYPE_STRUCTURED:
                    dealStructuralValue(netValueDisclosureT0, idMap);
                    break;
            }
        });
        return tableData;
    }

    /**
     * 处理平层净值数据
     *
     * @param data  待处理净值数据
     * @param idMap 账套基础数据
     */
    private void dealFlatValue(ValuationTableData data, Map<String, AccountInformation> idMap) {
        if (data == null || idMap == null || idMap.isEmpty()) {
            return;
        }
        String productId = data.getProductId();
        AccountInformation accountInformation = idMap.get(productId);
        if (accountInformation != null) {
            String productCode = accountInformation.getProductCode();
            String fullProductName = accountInformation.getFullProductName();
            data.setProductCode(productCode);
            data.setProductName(fullProductName);
        }
    }

    /**
     * 处理结构化净值数据
     *
     * @param data 待处理净值数据
     */
    private void dealStructuralValue(ValuationTableData data, Map<String, AccountInformation> idMap) {
        if (data == null || idMap == null || idMap.isEmpty()) {
            return;
        }
        String allCode = data.getSuperiorProductCode();
        String allName = data.getSuperiorProductName();
        String productId = data.getProductId();
        AccountInformation accountInformation = idMap.get(productId);
        if (allName.contains("_") && allCode.contains("_")) {
            String[] splitName = allName.split("_");
            String[] splitCode = allCode.split("_");
            String superiorCode = null;
            String superiorName = null;
            String inferiorCode = null;
            String inferiorName = null;
            for (int i = 0; i < splitName.length; i++) {
                String name = splitName[i];
                if (name.contains(INFERIOR)) {
                    inferiorName = name;
                    inferiorCode = splitCode[i];
                }
                if (name.contains(SUPERIOR) || name.contains(SUPERIOR_2)) {
                    superiorName = name;
                    superiorCode = splitCode[i];
                }
            }
            data.setSuperiorProductName(superiorName);
            data.setSuperiorProductCode(superiorCode);
            data.setInferiorProductName(inferiorName);
            data.setInferiorProductCode(inferiorCode);
        }
        if (accountInformation != null) {
            String productCode = accountInformation.getProductCode();
            String fullProductName = accountInformation.getFullProductName();
            data.setProductCode(productCode);
            data.setProductName(fullProductName);
        }
    }

    public Page<NetValueDisclosureVO> pageList(NetValueDisclosureParam netValueDisclosureParam,
            IPage<NetValueDisclosureVO> page) {
        return this.getBaseMapper().listPage(netValueDisclosureParam, page);
    }

    public List<NetValueDisclosure> syncValuationData(List<String> productIds, String dataDate, String valuationTime) {
        log.info("开始同步估值表数据, 需要同步的数据ID为:{}, 日期为:{}", productIds, dataDate);
        List<NetValueDisclosure> needDeal = this.getData(productIds, dataDate);
        this.doValuationTableGenerate(dataDate, needDeal);
        this.doReconStatus(dataDate, needDeal);
        this.doValuationTableConfirm(dataDate, needDeal);
        this.dealSecondary(dataDate, needDeal, valuationTime);
        NetValueDisclosureService service = (NetValueDisclosureService) AopContext.currentProxy();
        service.saveOrUpdateBatch(needDeal);
        log.info("同步结束");
        return needDeal;
    }

    public void doReconStatus(String dataDate, List<NetValueDisclosure> needDeal) {
        if (needDeal != null && !needDeal.isEmpty()) {
            List<ValuationTableStatus> reconciliationStatus = valuationDBMapper.getReconciliationStatus(dataDate);
            Map<String, ValuationTableStatus> reconciliationStatusMap = reconciliationStatus.stream()
                    .collect(Collectors.toMap(ValuationTableStatus::getProductId,
                            status -> status,
                            (oldOne, newOne) -> newOne));
            needDeal.stream()
                    .filter(netValueDisclosure -> netValueDisclosure.getManualReconConfirm() == 0)
                    .forEach(netValueDisclosure -> {
                        String productId = netValueDisclosure.getProductId();
                        ValuationTableStatus valuationTableStatus = reconciliationStatusMap.get(productId);
                        if (valuationTableStatus != null) {
                            netValueDisclosure.setReconciliationStatus(valuationTableStatus.getReconciliationStatus());
                        }
                    });
        }
    }

    public void sync(JobExecutionContext context, List<String> productIds, ValuationTime valuationTime) {
        if (productIds == null || productIds.isEmpty()) {
            log.info("产品ID列表为空，不执行同步操作");
            return;
        }
        String dataDate = null;
        try {
            switch (valuationTime) {
                case T0:
                    dataDate = this.getBaseMapper().getDate(DATE_KEY_T0);
                    log.info("获取T0估值日期: {}", dataDate);
                    break;
                case T1:
                    dataDate = this.getBaseMapper().getDate(DATE_KEY_T1);
                    log.info("获取T1估值日期: {}", dataDate);
                    break;
            }
        } catch (Exception e) {
            log.error("获取估值日期异常: {}", e.getMessage(), e);
        }
        if (dataDate == null) {
            dataDate = cronService.getFormatDataDate(context);
            log.info("从上下文获取估值日期: {}", dataDate);
        }

        log.info("开始同步估值系统信息，估值时间类型: {}, 日期: {}, 产品数量: {}", valuationTime, dataDate, productIds.size());
        // 同步估值系统信息
        NetValueDisclosureService service = (NetValueDisclosureService) AopContext.currentProxy();
        List<NetValueDisclosure> netValueDisclosures = service.syncValuationData(productIds, dataDate,
                valuationTime.name());
        log.info("同步估值系统信息完成，获取到记录数: {}", netValueDisclosures.size());

        // 查询生成估值表数据的,并且估值表和余额表没有生成
        List<NetValueDisclosure> needDownloadValuation = netValueDisclosures.stream()
                .filter(netValueDisclosure -> netValueDisclosure.getValuationTableGenerated() == 1)
                .filter(netValueDisclosure -> netValueDisclosure.getValuationTablePath() == null)
                .collect(Collectors.toList());
        log.info("需要首次下载估值表的记录数: {}", needDownloadValuation.size());
        if (!needDownloadValuation.isEmpty()) {
            log.info("需要首次下载估值表的产品ID: {}",
                    needDownloadValuation.stream().map(NetValueDisclosure::getProductId).collect(Collectors.toList()));
        }

        List<NetValueDisclosure> needDownloadBalance = netValueDisclosures.stream()
                .filter(netValueDisclosure -> netValueDisclosure.getValuationTableGenerated() == 1)
                .filter(netValueDisclosure -> netValueDisclosure.getBalanceTablePath() == null)
                .collect(Collectors.toList());
        log.info("需要下载余额表的记录数: {}", needDownloadBalance.size());

        // 调用下载估值表和余额表
        log.info("需要下载估值表表的内容: {}", needDownloadValuation);
        startDownloadValuationJobSimple(dataDate, needDownloadValuation, false);
        log.info("需要下载余额表的内容: {}", needDownloadBalance);
        startDownloadBalanceJobSimple(dataDate, needDownloadBalance);

        // 发送托管对账邮件 已有估值表并且状态为未发送的（排除正在发送的）
        LambdaQueryWrapper<NetValueDisclosure> netValueDisclosureLambdaQueryWrapper = new LambdaQueryWrapper<>();
        netValueDisclosureLambdaQueryWrapper.eq(NetValueDisclosure::getValuationDate, dataDate);
        netValueDisclosureLambdaQueryWrapper.eq(NetValueDisclosure::getCustodyReconciliationEmailSent,
                MailStatus.UNSENT.name());
        netValueDisclosureLambdaQueryWrapper.isNotNull(NetValueDisclosure::getValuationTablePath);
        List<NetValueDisclosure> needSendCustodyMail = this.list(netValueDisclosureLambdaQueryWrapper);
        log.info("需要发送托管对账邮件的记录数: {}", needSendCustodyMail.size());

        List<String> custodianBankJobIds = cronService.getJobIdByClass(CustodianBankMailSendJob.class);
        doSendMailSimple(needSendCustodyMail, dataDate, custodianBankJobIds, true);

        // 查询已确认但是未披露的
        log.info("再次同步估值系统信息，检查确认状态");
        service.syncValuationData(productIds, dataDate,
                valuationTime.name());
        netValueDisclosureLambdaQueryWrapper.clear();
        netValueDisclosureLambdaQueryWrapper.eq(NetValueDisclosure::getValuationDate, dataDate);
        netValueDisclosureLambdaQueryWrapper.in(NetValueDisclosure::getProductId, productIds);
        netValueDisclosureLambdaQueryWrapper.eq(NetValueDisclosure::getValuationTableConfirmed, 1);
        netValueDisclosureLambdaQueryWrapper.eq(NetValueDisclosure::getNetValueDisclosed, 0);
        List<NetValueDisclosure> needDisclosure = this.list(netValueDisclosureLambdaQueryWrapper);
        log.info("需要披露的记录数: {}", needDisclosure.size());
        log.info("需要披露的数据: {}", needDisclosure);
        sendNetValueDisclosureSimple(needDisclosure, dataDate);

        // 披露完成的再次下载 - 修改查询条件，确保第一次下载已完成且成功
        log.info("开始查询需要二次下载的估值表");
        netValueDisclosureLambdaQueryWrapper.clear();
        netValueDisclosureLambdaQueryWrapper.eq(NetValueDisclosure::getValuationDate, dataDate);
        netValueDisclosureLambdaQueryWrapper.in(NetValueDisclosure::getProductId, productIds);
        netValueDisclosureLambdaQueryWrapper.eq(NetValueDisclosure::getValuationTableConfirmed, 1);
        netValueDisclosureLambdaQueryWrapper.eq(NetValueDisclosure::getRpaSecondStatus,
                ValuationRPAStatus.NOT_STARTED.name());
        // 添加条件确保第一次下载已完成
        netValueDisclosureLambdaQueryWrapper.eq(NetValueDisclosure::getRpaStatus, ValuationRPAStatus.COMPLETED.name());
        // 添加条件确保第一次下载成功
        netValueDisclosureLambdaQueryWrapper.eq(NetValueDisclosure::getValuationTableDownloaded,
                ValuationDownloadStatus.SUCCESS.name());

        List<NetValueDisclosure> needDownload = this.list(netValueDisclosureLambdaQueryWrapper);
        log.info("需要二次下载估值表的记录数: {}", needDownload.size());
        if (!needDownload.isEmpty()) {
            log.info("需要二次下载估值表的产品ID: {}",
                    needDownload.stream().map(NetValueDisclosure::getProductId).collect(Collectors.toList()));
        }
        startDownloadValuationJobSimple(dataDate, needDownload, true);

        // 估值表发送
        log.info("开始查询需要发送估值表的记录");
        netValueDisclosureLambdaQueryWrapper.clear();
        netValueDisclosureLambdaQueryWrapper.eq(NetValueDisclosure::getValuationDate, dataDate);
        netValueDisclosureLambdaQueryWrapper.eq(NetValueDisclosure::getValuationTableDownloaded,
                ValuationDownloadStatus.SUCCESS.name());
        netValueDisclosureLambdaQueryWrapper.eq(NetValueDisclosure::getValuationTableSent, MailStatus.UNSENT.name());
        netValueDisclosureLambdaQueryWrapper.eq(NetValueDisclosure::getRpaSecondStatus, ValuationRPAStatus.COMPLETED.name());
        setT1Filter(netValueDisclosureLambdaQueryWrapper, valuationTime, productIds);
        List<NetValueDisclosure> needSendInvest = this.list(netValueDisclosureLambdaQueryWrapper);
        log.info("需要发送估值表的记录数: {}", needSendInvest.size());

        List<String> investorBankJobIds = cronService.getJobIdByClass(InvestorMailSendJob.class);
        doSendMailSimple(needSendInvest, dataDate, investorBankJobIds, false);

        // 报表发送
        log.info("开始查询需要发送报表的记录");
        netValueDisclosureLambdaQueryWrapper.clear();
        netValueDisclosureLambdaQueryWrapper.eq(NetValueDisclosure::getValuationDate, dataDate);
        netValueDisclosureLambdaQueryWrapper.eq(NetValueDisclosure::getInvestorReportSent, MailStatus.UNSENT.name());
        netValueDisclosureLambdaQueryWrapper.eq(NetValueDisclosure::getValuationTableConfirmed, 1);
        setT1Filter(netValueDisclosureLambdaQueryWrapper, valuationTime, productIds);
        List<NetValueDisclosure> needSendReport = this.list(netValueDisclosureLambdaQueryWrapper);
        log.info("需要发送报表的记录数: {}", needSendReport.size());

        List<String> investorStatementMailSendJobIds = cronService.getJobIdByClass(InvestorStatementMailSendJob.class);
        doSendMailSimple(needSendReport, dataDate, investorStatementMailSendJobIds, false);

        // 三方发送
        log.info("开始查询需要三方发送的记录");
        netValueDisclosureLambdaQueryWrapper.clear();
        netValueDisclosureLambdaQueryWrapper.eq(NetValueDisclosure::getValuationDate, dataDate);
        netValueDisclosureLambdaQueryWrapper.eq(NetValueDisclosure::getValuationTableConfirmed, 1);
        netValueDisclosureLambdaQueryWrapper.eq(NetValueDisclosure::getThirdPartySent, MailStatus.UNSENT.name());
        setT1Filter(netValueDisclosureLambdaQueryWrapper, valuationTime, productIds);
        List<NetValueDisclosure> needSendThird = this.list(netValueDisclosureLambdaQueryWrapper);
        log.info("需要三方发送的记录数: {}", needSendThird.size());

        List<String> thirdJobIds = cronService.getJobIdByClass(ThirdMailSendJob.class);
        doSendMailSimple(needSendThird, dataDate, thirdJobIds, false);

        log.info("同步流程执行完成，估值时间类型: {}, 日期: {}", valuationTime, dataDate);
    }

    private void setT1Filter(LambdaQueryWrapper<NetValueDisclosure> queryWrapper, ValuationTime valuationTime, List<String> productIds) {
        if (ValuationTime.T1.equals(valuationTime)) {
            List<String> t1ProductIds = dealT1Batch();
            if (!t1ProductIds.isEmpty()) {
                queryWrapper.in(NetValueDisclosure::getProductId, t1ProductIds);
            }
        }else{
            queryWrapper.in(NetValueDisclosure::getProductId, productIds);
        }
    }

    @NonNull
    public List<String> dealT1Batch() {
        List<NetValueDisclosureT1Batch> batches = netValueDisclosureT1BatchService.list();
        Map<Integer, NetValueDisclosureT1Batch> batchMap = batches.stream()
                .collect(Collectors.toMap(NetValueDisclosureT1Batch::getId, batch -> batch, (b1, b2) -> b2));
        NetValueDisclosureT1Batch firstNetValueDisclosureT1Batch = batchMap.get(1);
        NetValueDisclosureT1Batch secondNetValueDisclosureT1Batch = batchMap.get(2);
        if (firstNetValueDisclosureT1Batch != null && secondNetValueDisclosureT1Batch != null) {
            Date firstBatchTime = firstNetValueDisclosureT1Batch.getBatchTime();
            LocalTime firstTime = transDateToLocalDate(firstBatchTime);
            Date secondBatchTime = secondNetValueDisclosureT1Batch.getBatchTime();
            LocalTime secondTime = transDateToLocalDate(secondBatchTime);
            LocalTime nowTime = transDateToLocalDate(new Date());
            boolean noExecute = nowTime.isBefore(firstTime);
            boolean executeFirst = nowTime.equals(firstTime)
                    || (nowTime.isAfter(firstTime) && nowTime.isBefore(secondTime));
            boolean executeSecond = nowTime.isAfter(secondTime) || nowTime.equals(secondTime);
            if (noExecute) {
                return new ArrayList<>();
            } else if (executeFirst) {
                String productIds = firstNetValueDisclosureT1Batch.getProductIds();
                if (productIds == null) {
                    return new ArrayList<>();
                }
                return Arrays.stream(productIds.split(",")).collect(Collectors.toList());
            } else if (executeSecond) {
                String firstProductIds = firstNetValueDisclosureT1Batch.getProductIds();
                String secondProductIds = secondNetValueDisclosureT1Batch.getProductIds();
                if (firstProductIds == null || secondProductIds == null) {
                    return new ArrayList<>();
                }
                List<String> firstList = Arrays.stream(firstProductIds.split(",")).collect(Collectors.toList());
                List<String> secondList = Arrays.stream(secondProductIds.split(",")).collect(Collectors.toList());
                firstList.addAll(secondList);
                return firstList;
            }
        }
        return new ArrayList<>();
    }

    private LocalTime transDateToLocalDate(Date date) {
        if (date == null) {
            throw new RuntimeException("日期为空");
        }
        return date.toInstant().atZone(ZoneId.of(BaseConstant.DEFAULT_TIME_ZONE)).toLocalTime();
    }

    /**
     * 根据任务类型更新邮件状态为发送中
     */
    private List<NetValueDisclosure> updateMailStatusToSending(List<NetValueDisclosure> dataList, List<String> jobIds) {
        if (dataList.isEmpty() || jobIds.isEmpty()) {
            return List.of();
        }

        // 根据jobId确定邮件类型
        String jobClassName = cronService.getJobs(jobIds).get(0).getClassName();

        List<String> ids = dataList.stream().map(NetValueDisclosure::getId).collect(Collectors.toList());
        LambdaUpdateWrapper<NetValueDisclosure> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.in(NetValueDisclosure::getId, ids);

        switch (jobClassName) {
            case "CustodianBankMailSendJob":
                updateWrapper.set(NetValueDisclosure::getCustodyReconciliationEmailSent, MailStatus.SENDING.name());
                log.info("已将 {} 个产品的托管对账邮件状态更新为SENDING", dataList.size());
                break;
            case "InvestorMailSendJob":
                updateWrapper.set(NetValueDisclosure::getValuationTableSent, MailStatus.SENDING.name());
                log.info("已将 {} 个产品的估值表邮件状态更新为SENDING", dataList.size());
                break;
            case "InvestorStatementMailSendJob":
                updateWrapper.set(NetValueDisclosure::getInvestorReportSent, MailStatus.SENDING.name());
                log.info("已将 {} 个产品的报表邮件状态更新为SENDING", dataList.size());
                break;
            case "ThirdMailSendJob":
                updateWrapper.set(NetValueDisclosure::getThirdPartySent, MailStatus.SENDING.name());
                log.info("已将 {} 个产品的三方邮件状态更新为SENDING", dataList.size());
                break;
            case "NetValueDisclosureMailSendJob":
                // 净值披露邮件使用特殊的锁机制，不在这里处理
                log.info("净值披露邮件使用专门的锁机制，跳过状态更新");
                break;
            default:
                log.warn("未知的邮件任务类型: {}", jobClassName);
                break;
        }

        this.update(updateWrapper);
        return this.listByIds(ids);
    }

}
