package cn.sdata.om.al.audit.controller;

import cn.sdata.om.al.audit.entity.NetValueOfAccountSet;
import cn.sdata.om.al.audit.entity.PortfolioNetValueWarning;
import cn.sdata.om.al.audit.service.NetValueOfAccountSetService;
import cn.sdata.om.al.entity.CommonPageParam;
import cn.sdata.om.al.mapper.NetValueDisclosureMapper;
import cn.sdata.om.al.result.R;
import cn.sdata.om.al.vo.NetValueOfAccountSetVO;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 多账套净值信息查询
 */
@RestController
@RequestMapping("/audit/net_value_of_account_set")
@AllArgsConstructor
public class NetValueOfAccountSetController {

    private NetValueOfAccountSetService netValueOfAccountSetService;
    private final NetValueDisclosureMapper netValueDisclosureMapper;

    @PostMapping("page")
    public R<Page<NetValueOfAccountSetVO>> page(@RequestBody NetValueOfAccountSetVO commonPageParam) {
        Page<NetValueOfAccountSetVO> page = netValueOfAccountSetService.selectPageInfo(commonPageParam);
        return R.ok(page);
    }


    @GetMapping("getComparisonDay")
    public R<String> getComparisonDay() {
        return R.ok(netValueDisclosureMapper.getDate("comparisonDay"),"");
    }

    @GetMapping("setComparisonDay")
    public R<String> setComparisonDay(@RequestParam String comparisonDay) {
        netValueDisclosureMapper.setComparisonDay(comparisonDay,"comparisonDay");
        return R.ok();
    }
}
