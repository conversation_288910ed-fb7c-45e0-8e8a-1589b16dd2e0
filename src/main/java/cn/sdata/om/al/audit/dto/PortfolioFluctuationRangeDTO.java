package cn.sdata.om.al.audit.dto;

import cn.sdata.om.al.audit.enums.CompareType;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 组合净值波动范围规则DTO，用于前端展示
 * 将相同规则的多个账套合并显示
 */
@Data
public class PortfolioFluctuationRangeDTO {
    
    /**
     * 产品/账套列表
     */
    private List<Portfolio> portfolios;
    
    /**
     * 下限比较类型：> 或 >=
     */
    private CompareType lowerCompareType;
    
    /**
     * 上限比较类型：< 或 <=
     */
    private CompareType upperCompareType;
    
    /**
     * 下限值
     */
    private BigDecimal lowerLimit;
    
    /**
     * 上限值
     */
    private BigDecimal upperLimit;
    
    /**
     * 创建时间
     */
    private Date createTime;
    
    /**
     * 更新时间
     */
    private Date updateTime;
    
    /**
     * 产品/账套信息
     */
    @Data
    public static class Portfolio {
        /**
         * 产品/账套ID
         */
        private String productId;
        
        /**
         * 产品/账套名称
         */
        private String productName;
    }
} 