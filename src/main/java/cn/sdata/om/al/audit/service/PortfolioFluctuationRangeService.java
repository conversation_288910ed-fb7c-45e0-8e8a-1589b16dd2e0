package cn.sdata.om.al.audit.service;

import cn.sdata.om.al.audit.dto.PortfolioFluctuationRangeDTO;
import cn.sdata.om.al.audit.entity.PortfolioFluctuationRange;
import com.baomidou.mybatisplus.extension.service.IService;

import java.math.BigDecimal;
import java.util.List;

/**
 * 组合净值波动范围规则Service接口
 */
public interface PortfolioFluctuationRangeService extends IService<PortfolioFluctuationRange> {

    /**
     * 保存单条波动范围规则
     * @param rangeDTO 波动范围规则DTO
     * @return 是否保存成功
     */
    boolean saveFluctuationRange(PortfolioFluctuationRangeDTO rangeDTO);

    /**
     * 保存波动范围规则列表（整体替换）
     * @param rangeDTOList 波动范围规则DTO列表
     * @return 是否保存成功
     */
    boolean saveFluctuationRanges(List<PortfolioFluctuationRangeDTO> rangeDTOList);

    /**
     * 获取所有波动范围规则（按规则合并后的结果）
     * @return 波动范围规则DTO列表
     */
    List<PortfolioFluctuationRangeDTO> listAllRanges();

    /**
     * 验证波动率是否在指定范围内
     * @param fluctuationRate 波动率
     * @param productId 产品ID
     * @return 是否在范围内
     */
    boolean validateFluctuationRate(BigDecimal fluctuationRate, String productId);

    /**
     * 验证波动率是否在指定范围内
     * @param fluctuationRate 波动率
     * @param productId  产品id
     * @param ranges 所有波动规则
     * @return 是否在范围内
     */
    boolean validateFluctuationRateByRangeList(BigDecimal fluctuationRate, String productId, List<PortfolioFluctuationRange> ranges);
}
