package cn.sdata.om.al.audit.service;

import cn.sdata.om.al.entity.CommonPageParam;
import cn.sdata.om.al.vo.NetValueOfAccountSetVO;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import cn.sdata.om.al.audit.entity.NetValueOfAccountSet;

/**
* <AUTHOR>
* @description 针对表【net_value_of_account_set(多账套净值信息)】的数据库操作Service
* @createDate 2025-07-23 10:09:52
*/
public interface NetValueOfAccountSetService extends IService<NetValueOfAccountSet> {

    void syncNetValueFluctuation(String today);

    Page<NetValueOfAccountSetVO> selectPageInfo(NetValueOfAccountSetVO commonPageParam);
}
