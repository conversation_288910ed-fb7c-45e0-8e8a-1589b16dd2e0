package cn.sdata.om.al.disclosure;

import cn.sdata.om.al.entity.*;
import cn.sdata.om.al.entity.mail.vo.MailTemplateDetailVo;
import cn.sdata.om.al.mapper.TADBMapper;
import cn.sdata.om.al.service.AccountInformationService;
import cn.sdata.om.al.service.NetValueDisclosureService;
import cn.sdata.om.al.utils.ExcelUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Component;

import java.io.ByteArrayOutputStream;
import java.util.*;
import java.util.stream.Collectors;

@Component
@AllArgsConstructor
@Slf4j
public class MarketPriceHandler implements MailHandler {

    private final NetValueDisclosureService netValueDisclosureService;
    private final AccountInformationService accountInformationService;
    private final TADBMapper tadbMapper;

    @Override
    public SendMailInfo execute(SendMailInfo sendMailInfo, String dataDate, Map<String, RemoteFileInfo> files, MailTemplateDetailVo mailTemplateDetailVo) {
        Objects.requireNonNull(sendMailInfo, "邮件信息不得为空");
        Objects.requireNonNull(mailTemplateDetailVo, "邮件模板不得为空");
        List<String> productIds = sendMailInfo.getProductIds();
        Map<String, String> idCodes = accountInformationService.list().stream().collect(Collectors.toMap(AccountInformation::getId, AccountInformation::getProductCode, (oldOne, newOne) -> newOne));
        List<String> productCodes = productIds.stream().map(idCodes::get).filter(Objects::nonNull).collect(Collectors.toList());
        List<ValuationTableData> netValueFromValuation = netValueDisclosureService.getNetValueFromValuation(dataDate, productIds);
        Map<String, ValuationTableData> valuationMap = netValueFromValuation.stream()
                .collect(Collectors.toMap
                        (data -> Optional.ofNullable(idCodes.get(data.getProductId())).orElse(""),
                                data -> data,
                                (oldOne, newOne) -> newOne));
        List<MarketPriceData> marketPriceData = tadbMapper.queryMarketPrice(productCodes);
        byte[] attachment = generateFile(marketPriceData, valuationMap);
        Map<String, byte[]> attachments = sendMailInfo.getAttachment();
        attachments.put("安联保险资产管理有限公司_行情_" + dataDate + ".xlsx", attachment);
        sendMailInfo.setSubject(mailTemplateDetailVo.getTitle());
        sendMailInfo.setContent(mailTemplateDetailVo.getContent());
        return sendMailInfo;
    }


    private byte[] generateFile(List<MarketPriceData> marketPriceData, Map<String, ValuationTableData> valuationMap) {
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        try (XSSFWorkbook workbook = new XSSFWorkbook()) {
            XSSFSheet sheet = workbook.createSheet();
            XSSFRow header = sheet.createRow(0);
            List<String> headerNames = List.of("账户名称", "资管计划名称", "市场代码", "估值日期", "单位净值", "万份收益", "份额");
            CellStyle headerCellStyle = ExcelUtil.getCellStyle(workbook, true);
            CellStyle cellStyle = ExcelUtil.getCellStyle(workbook, false);
            int[] widthList = new int[headerNames.size()];
            for (int i = 0; i < headerNames.size(); i++) {
                String headerName = headerNames.get(i);
                int width = ExcelUtil.getWidth(headerName);
                widthList[i] = width;
                XSSFCell cell = header.createCell(i);
                cell.setCellStyle(headerCellStyle);
                cell.setCellValue(headerName);
            }
            for (int i = 0; i < marketPriceData.size(); i++) {
                MarketPriceData marketPriceDatum = marketPriceData.get(i);
                XSSFRow row = sheet.createRow(i + 1);
                String marketCode = marketPriceDatum.getMarketCode();
                ValuationTableData valuationTableData = valuationMap.get(marketCode);
                if (valuationTableData != null) {
                    marketPriceDatum.setUnitNetValue(valuationTableData.getNetValue());
                    marketPriceDatum.setValuationDate(valuationTableData.getValuationDate());
                    String accountName = marketPriceDatum.getAccountName();
                    if (accountName.contains("货币")) {
                        marketPriceDatum.setProfitPerTenThousand(valuationTableData.getNetValue());
                    } else {
                        marketPriceDatum.setUnitNetValue(valuationTableData.getNetValue());
                    }
                }
                XSSFCell cell = row.createCell(0);
                cell.setCellStyle(cellStyle);
                String accountName = marketPriceDatum.getAccountName();
                cell.setCellValue(accountName);
                ExcelUtil.setWidthList(sheet, widthList, 0, accountName);
                cell = row.createCell(1);
                cell.setCellStyle(cellStyle);
                String planName = marketPriceDatum.getPlanName();
                cell.setCellValue(planName);
                ExcelUtil.setWidthList(sheet, widthList, 1, planName);
                cell = row.createCell(2);
                cell.setCellStyle(cellStyle);
                cell.setCellValue(marketCode);
                ExcelUtil.setWidthList(sheet, widthList, 2, marketCode);
                String valuationDate = marketPriceDatum.getValuationDate();
                cell = row.createCell(3);
                cell.setCellStyle(cellStyle);
                cell.setCellValue(valuationDate);
                ExcelUtil.setWidthList(sheet, widthList, 3, valuationDate);
                cell = row.createCell(4);
                cell.setCellStyle(cellStyle);
                String unitNetValue = marketPriceDatum.getUnitNetValue();
                log.info("unitNetValue:{}", ExcelUtil.getWidth(unitNetValue));
                cell.setCellValue(unitNetValue);
                ExcelUtil.setWidthList(sheet, widthList, 4, valuationDate);
                cell = row.createCell(5);
                cell.setCellStyle(cellStyle);
                String profitPerTenThousand = marketPriceDatum.getProfitPerTenThousand();
                cell.setCellValue(profitPerTenThousand);
                ExcelUtil.setWidthList(sheet, widthList, 5, profitPerTenThousand);
                cell = row.createCell(6);
                cell.setCellStyle(cellStyle);
                String shares = marketPriceDatum.getShares();
                cell.setCellValue(shares);
                ExcelUtil.setWidthList(sheet, widthList, 6, shares);
            }
            workbook.write(out);
        }catch (Exception e){
            throw new RuntimeException("投资人行情表生成失败", e);
        }
        return out.toByteArray();
    }

}
