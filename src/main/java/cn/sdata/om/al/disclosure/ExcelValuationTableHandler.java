package cn.sdata.om.al.disclosure;

import cn.sdata.om.al.entity.RemoteFileInfo;
import cn.sdata.om.al.entity.SendMailInfo;
import cn.sdata.om.al.entity.mail.vo.MailTemplateDetailVo;
import cn.sdata.om.al.utils.SMBManager;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;

import static cn.sdata.om.al.utils.StringUtil.concatSeparator;

/**
 * 发送excel类型估值表以及余额表
 * 把RPA下载的文件放入附件即可
 */
@Component
@AllArgsConstructor
public class ExcelValuationTableHandler implements MailHandler {

    private final SMBManager smbManager;

    @Override
    public SendMailInfo execute(SendMailInfo sendMailInfo, String dataDate, Map<String, RemoteFileInfo> files, MailTemplateDetailVo mailTemplateDetailVo) {
        Objects.requireNonNull(sendMailInfo, "邮件信息不得为空");
        Objects.requireNonNull(mailTemplateDetailVo, "邮件模板不得为空");
        if (files == null || files.isEmpty()) {
            throw new RuntimeException("RPA下载文件为空");
        }
        List<String> productIds = sendMailInfo.getProductIds();
        for (String productId : productIds) {
            RemoteFileInfo remoteFileInfo = files.get(productId);
            if (remoteFileInfo != null) {
                Map<String, byte[]> attachment = sendMailInfo.getAttachment();
                byte[] bytes = smbManager.downloadFile(concatSeparator(remoteFileInfo.getRelativePath(), remoteFileInfo.getFileName()));
                attachment.put(remoteFileInfo.getFileName(), bytes);
            }
        }
        sendMailInfo.setSubject(mailTemplateDetailVo.getTitle());
        sendMailInfo.setContent(mailTemplateDetailVo.getContent());
        return sendMailInfo;
    }
}
