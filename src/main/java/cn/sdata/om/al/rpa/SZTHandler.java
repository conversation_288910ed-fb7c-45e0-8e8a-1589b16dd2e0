package cn.sdata.om.al.rpa;

import cn.sdata.om.al.entity.AccountInformation;
import cn.sdata.om.al.entity.NetValueDisclosure;
import cn.sdata.om.al.entity.RemoteFileInfo;
import cn.sdata.om.al.entity.ValuationTableRecords;
import cn.sdata.om.al.enums.ValuationDownloadStatus;
import cn.sdata.om.al.enums.ValuationRPAStatus;
import cn.sdata.om.al.service.AccountInformationService;
import cn.sdata.om.al.service.NetValueDisclosureService;
import cn.sdata.om.al.service.ValuationTableRecordsService;
import cn.sdata.om.al.utils.StringUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

import static cn.sdata.om.al.constant.JobConstant.*;

@Component
@AllArgsConstructor
@Slf4j
public class SZTHandler implements BaseHandler{

    private final NetValueDisclosureService netValueDisclosureService;
    private final ValuationTableRecordsService valuationTableRecordsService;

    @Override
    public void execute(Map<String, Object> param, List<RemoteFileInfo> files) {
        log.info("处理深证通状态");
        List<ValuationTableRecords> valuationTableRecordsList = new ArrayList<>();
        Map<String, NetValueDisclosure> netValueMap = getNetValueMap(param);
        Collection<NetValueDisclosure> values = netValueMap.values();
        values.forEach(netValueDisclosure -> netValueDisclosure.setSztStatus(ValuationRPAStatus.COMPLETED.name()));
        netValueDisclosureService.saveOrUpdateBatch(values);
        valuationTableRecordsService.saveBatch(valuationTableRecordsList);
    }

    @Override
    @SuppressWarnings("unchecked")
    public void onFail(Map<String, Object> param) {
        Objects.requireNonNull(param, "RPA参数不得为空");
        List<NetValueDisclosure> list = (List<NetValueDisclosure>) param.get(NET_VALUE_DISCLOSURE_LIST);
        list.forEach(netValueDisclosure -> netValueDisclosure.setSztStatus(ValuationRPAStatus.COMPLETED.name()));
        netValueDisclosureService.saveOrUpdateBatch(list);
    }

}
