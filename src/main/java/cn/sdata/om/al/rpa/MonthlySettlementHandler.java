package cn.sdata.om.al.rpa;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.IdUtil;
import cn.sdata.om.al.entity.FlowList;
import cn.sdata.om.al.entity.RemoteFileInfo;
import cn.sdata.om.al.mapper.BaseCronLogMapper;
import cn.sdata.om.al.qrtz.entity.BaseCronLog;
import cn.sdata.om.al.service.RemoteFileInfoService;
import cn.sdata.om.al.service.impl.MonthlySettlementServiceImpl;
import cn.sdata.om.al.utils.LogMSUtil;
import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.PropertyPlaceholderHelper;

import java.io.File;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.attribute.BasicFileAttributes;
import java.util.*;
import java.util.concurrent.TimeUnit;

import static cn.sdata.om.al.constant.RPAConstant.LOG_ID;

@Slf4j
@Component
public class MonthlySettlementHandler extends MonthlySettlementHandlerAdapter {

    private BaseCronLogMapper baseCronLogMapper;

    @Value("${file.monthly-settlement-dir}")
    private String localBasePath;

    private MonthlySettlementServiceImpl.OrdersAndFlowConfig ordersAndFlowConfig;

    private RemoteFileInfoService remoteFileInfoService;

    @Value("${smb.base}")
    private String smbBasePath;

    @Value("${smb.host}")
    private String smbHost;

    @Autowired
    public void setOrdersAndFlowConfig(MonthlySettlementServiceImpl.OrdersAndFlowConfig ordersAndFlowConfig) {
        this.ordersAndFlowConfig = ordersAndFlowConfig;
    }

    @Autowired
    public void setBaseCronLogMapper(BaseCronLogMapper baseCronLogMapper) {
        this.baseCronLogMapper = baseCronLogMapper;
    }

    @Autowired
    public void setRemoteFileInfoService(RemoteFileInfoService remoteFileInfoService) {
        this.remoteFileInfoService = remoteFileInfoService;
    }

    @Override
    public void execute(Map<String, Object> param, FlowList flowList, String logId) {
        log.info("开始调度月结任务RPA任务后的处理...");
        try {
            while (!MonthlySettlementServiceImpl.isContinueExeHandler) {
                TimeUnit.SECONDS.sleep(2);
            }
            String basePath = flowList.getBasePath();
            BaseCronLog baseCronLog = baseCronLogMapper.selectById(logId);
            Map<String, String> config = ordersAndFlowConfig.getConfig();
            Map<String, String> reverse = MapUtil.reverse(config);
            Integer id = flowList.getId();
            String order = reverse.get(id + "");
            if (null != baseCronLog) {
                String dataDate = baseCronLog.getDataDate();
                String[] split = dataDate.split("-");
                if (ArrayUtil.isNotEmpty(split)) {
                    String beginDataDate = split[0].replaceAll("年", "-").replaceAll("月", "-").replaceAll("日", "");
                    String downloadPath;
                    if (basePath.matches(".*\\$\\{.*}.*")) {
                        PropertyPlaceholderHelper propertyPlaceholderHelper = new PropertyPlaceholderHelper("${", "}");
                        Properties properties = new Properties();
                        properties.put("yyyyMMdd", DateUtil.format(DateUtil.parseDate(beginDataDate), "yyyyMMdd"));
                        downloadPath = propertyPlaceholderHelper.replacePlaceholders(basePath, properties);
                    } else {
                        downloadPath = basePath + File.separator + DateUtil.format(DateUtil.parseDate(beginDataDate), "yyyyMMdd");
                    }
                    String format = DateUtil.format(DateUtil.parseDate(beginDataDate), "yyyy-MM");
                    String folderPath = localBasePath + File.separator + format + File.separator + order + "." + flowList.getShowName();
                    log.info("folderPath = {}", folderPath);
                    List<String> fileNames = FileUtil.listFileNames(folderPath);
                    List<RemoteFileInfo> remoteFileInfos = new ArrayList<>();
                    int i = 0;
                    for (String fileName : fileNames) {
                        File file = new File(folderPath + File.separator + fileName);
                        RemoteFileInfo remoteFileInfo = new RemoteFileInfo();
                        remoteFileInfo.setId(IdUtil.getSnowflakeNextIdStr());
                        remoteFileInfo.setFileName(fileName);
                        remoteFileInfo.setFilePath("\\\\".concat(smbHost).concat("\\").concat(smbBasePath).concat("\\").concat(downloadPath));
                        remoteFileInfo.setRelativePath(fileName);
                        remoteFileInfo.setLocation("local");
                        remoteFileInfo.setLogId(logId);
                        BasicFileAttributes basicFileAttributes = Files.readAttributes(Path.of(file.getAbsolutePath()), BasicFileAttributes.class);
                        if (basicFileAttributes != null) {
                            remoteFileInfo.setDownloadTime(Date.from(basicFileAttributes.creationTime().toInstant()));
                            remoteFileInfo.setDealTime(Date.from(basicFileAttributes.creationTime().toInstant()));
                        }
                        remoteFileInfo.setFileSize(file.length());
                        remoteFileInfo.setSerialNumber(i + 1);
                        remoteFileInfos.add(remoteFileInfo);
                    }
                    if (CollectionUtil.isNotEmpty(remoteFileInfos)) {
                        remoteFileInfoService.saveBatch(remoteFileInfos);
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            MonthlySettlementServiceImpl.isContinueExeHandler = false;
        }
    }

    @Override
    public void onFail(Map<String, Object> param) {
        Object o = param.get(LOG_ID);
        if (o != null) {
            Object errorMsg = param.get("errorMsg");
            String rpaLogId = String.valueOf(o);
            JSONObject params = new JSONObject();
            params.put("rpaLogId", rpaLogId);
            params.put("exception", errorMsg != null ? String.valueOf(errorMsg) : "");
            params.put("endTime", DateUtil.now());
            LogMSUtil.postRpaLog(params);
        }
    }
}
