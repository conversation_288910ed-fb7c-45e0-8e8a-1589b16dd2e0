package cn.sdata.om.al.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.crypto.digest.MD5;
import cn.sdata.om.al.dto.PwdDto;
import cn.sdata.om.al.dto.RoleGrantMenuParamDto;
import cn.sdata.om.al.dto.RoleGrantUserParamDto;
import cn.sdata.om.al.dto.UserGrantRoleParamDto;
import cn.sdata.om.al.entity.*;
import cn.sdata.om.al.result.R;
import cn.sdata.om.al.service.*;
import cn.sdata.om.al.utils.EncryptionUtil;
import cn.sdata.om.al.utils.SecureUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2025/2/8 14:45
 * @Version 1.0
 */
@Slf4j
@RestController
@RequestMapping("/manage")
public class PermissionManageController {

    @Resource
    private UserService userService;

    @Resource
    private RoleService roleService;

    @Resource
    private UserRoleService userRoleService;

    @Resource
    private MenuService menuService;

    @Resource
    private RoleMenuService roleMenuService;

    @Resource
    private EncryptionUtil encryptionUtil;

    /**
     * 新增用户
     *
     * @param user
     * @return
     */
    @PostMapping("/user/save")
    public R<?> saveUser(@RequestBody User user) {
        try {
            Assert.notNull(user, "[用户不能为空]");
            Assert.notNull(user.getAccount(), "[账号不能为空]");
            Assert.notNull(user.getName(), "[名称不能为空]");
            Assert.notNull(user.getPassword(), "[密码不能为空]");
            user.setId(String.valueOf(System.nanoTime()))
                    .setPassword(MD5.create().digestHex(user.getPassword()))
                    .setCreateTime(new Date())
                    .setCreateUser(SecureUtil.currentUserId())
                    .setAdFlag(0);
            int count = userService.list(Wrappers.lambdaQuery(User.class).eq(User::getAccount, user.getAccount())).size();
            if (count > 0) {
                return R.failed("账号已存在，请重新输入");
            }
            Boolean flag = userService.save(user);
            return flag ? R.ok("创建成功") : R.failed("创建失败");
        } catch (Exception e) {
            log.error("user_save_error:{}", e);
            return R.failed(e.getMessage());
        }
    }

    /**
     * 删除用户
     *
     * @param user
     * @return
     */
    @DeleteMapping("/user/del")
    public R<?> delUser(@RequestBody User user) {
        try {
            Assert.notNull(user.getId(), "[id不能为空]");
            //删除用户
            userService.removeById(user.getId());
            //删除用户角色关联数据
            userRoleService.remove(Wrappers.lambdaQuery(UserRole.class).eq(UserRole::getUserId, user.getId()));
            return R.ok("删除成功");
        } catch (Exception e) {
            log.error("user_del_error:{}", e);
            return R.failed(e.getMessage());
        }
    }

    /**
     * 编辑用户
     *
     * @param user
     */
    @PostMapping("/user/update")
    public R<?> updateUser(@RequestBody User user) {
        try {
            Assert.notNull(user.getId(), "[id不能为空]");
            Assert.notNull(user.getAccount(), "[账号不能为空]");
            Assert.notNull(user.getName(), "[名称不能为空]");
            Boolean flag = userService.update(Wrappers.lambdaUpdate(User.class)
                    .eq(User::getId, user.getId())
                    .set(StringUtils.isNotEmpty(user.getAccount()), User::getAccount, user.getAccount())
                    .set(StringUtils.isNotEmpty(user.getName()), User::getName, user.getName())
                    .set(User::getMobile, user.getMobile())
                    .set(User::getEmail, user.getEmail())
                    .set(User::getUpdateTime, new Date())
                    .set(User::getUpdateUser, SecureUtil.currentUserId())
            );
            return flag ? R.ok("编辑成功") : R.failed("编辑失败");
        } catch (Exception e) {
            log.error("user_update_error:{}", e);
            return R.failed(e.getMessage());
        }
    }


    /**
     * 用户全量列表
     *
     * @param name    用户名称
     * @param account 账号
     * @return
     */
    @GetMapping("/user/list")
    public R<?> userList(String name, String account) {
        try {
            LambdaQueryWrapper<User> qw = new LambdaQueryWrapper<>();
            qw.select(User::getId, User::getAccount, User::getName, User::getMobile, User::getStatus,
                            User::getCreateUser, User::getCreateTime, User::getUpdateUser, User::getUpdateTime, User::getAdFlag)
                    .like(StringUtils.isNotBlank(account), User::getAccount, account)
                    .like(StringUtils.isNotBlank(name), User::getName, name)
                    .eq(User::getStatus, 1)
                    .orderByDesc(User::getCreateTime);
            if (!"1".equals(SecureUtil.currentUserId())) {
                qw.ne(User::getId, "1");
            }
            return R.ok(userService.list(qw));
        } catch (Exception e) {
            log.error("user_list_error:{}", e);
            return R.failed(e.getMessage());
        }
    }


    /**
     * 用户分页列表
     *
     * @param pageNo          当前页数
     * @param pageSize        每页大小
     * @param name            用户名称
     * @param account         账号
     * @param startDate       开始日期
     * @param endDate         结束日期
     * @param startUpdateDate 开始更新日期
     * @param endUpdateDate   结束更新日期
     * @return
     */
    @GetMapping("/user/page")
    public R<?> userPage(@RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                         @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                         String name,
                         String account,
                         String startDate,
                         String endDate,
                         String startUpdateDate,
                         String endUpdateDate) {
        try {
            LambdaQueryWrapper<User> qw = new LambdaQueryWrapper<>();
            qw.select(User::getId, User::getAccount, User::getName, User::getMobile, User::getEmail, User::getStatus,
                            User::getCreateUser, User::getCreateTime, User::getUpdateUser, User::getUpdateTime, User::getAdFlag)
                    .between((StringUtils.isNotBlank(startDate) && StringUtils.isNotBlank(endDate)), User::getCreateTime, startDate + " 00:00:00", endDate + " 23:59:59")
                    .between((StringUtils.isNotBlank(startUpdateDate) && StringUtils.isNotBlank(endUpdateDate)), User::getUpdateTime, startUpdateDate + " 00:00:00", endUpdateDate + " 23:59:59")
                    .like(StringUtils.isNotBlank(account), User::getAccount, account)
                    .like(StringUtils.isNotBlank(name), User::getName, name)
                    .eq(User::getStatus, 1)
                    .orderByDesc(User::getCreateTime);
            if (!"1".equals(SecureUtil.currentUserId())) {
                qw.ne(User::getId, "1");
            }
            Map<String, String> um = userService.list(Wrappers.lambdaQuery(User.class).select(User::getId, User::getName)).stream().collect(Collectors.toMap(User::getId, User::getName, (n1, n2) -> n1));
            Page<User> userPage = userService.page(new Page<>(pageNo, pageSize), qw);
            userPage.getRecords().forEach(user -> {
                if (StringUtils.isNotBlank(user.getCreateUser()) && um.containsKey(user.getCreateUser())) {
                    user.setCreateUser(um.get(user.getCreateUser()));
                }
                if (StringUtils.isNotBlank(user.getUpdateUser()) && um.containsKey(user.getUpdateUser())) {
                    user.setUpdateUser(um.get(user.getUpdateUser()));
                }
            });
            return R.ok(userPage);
        } catch (Exception e) {
            log.error("user_page_error:{}", e);
            return R.failed(e.getMessage());
        }
    }

    /**
     * 用户详情接口
     *
     * @param userId 用户id
     * @return
     */
    @GetMapping("/user/detail")
    public R<?> userDetail(@RequestParam("userId") String userId) {
        try {
            Assert.notNull(userId, "[id不能为空]");
            User user = userService.getById(userId);
            List<Role> roles = userService.getUser2roleList(userId);
            user.setPassword(null);
            if (null != roles && CollUtil.isNotEmpty(roles)) {
                user.setRoles(roles).setRoleIds(roles.stream().map(Role::getId).distinct().collect(Collectors.toList()));
            }
            List<Menu> menus = userService.getUser2menuList(user.getId());
            if (null != menus && CollUtil.isNotEmpty(menus)) {
                user.setMenus(MenuService.buildTree(menus));
            }
            return R.ok(user);
        } catch (Exception e) {
            log.error("user_detail_error:{}", e);
            return R.failed(e.getMessage());
        }
    }


    /**
     * 切换用户状态 1:开启状态(默认)，0:关闭状态
     *
     * @param user
     * @return
     */
    @PostMapping("/user/switchStatus")
    public R<?> switchStatus(@RequestBody User user) {
        try {
            Assert.notNull(user.getId(), "[id不能为空]");
            Assert.notNull(user.getStatus(), "[状态不能为空]");
            Boolean flag = userService.update(Wrappers.lambdaUpdate(User.class)
                    .eq(User::getId, user.getId())
                    .set(User::getStatus, user.getStatus())
                    .set(User::getUpdateTime, new Date())
                    .set(User::getUpdateUser, SecureUtil.currentUserId())
            );
            return flag ? R.ok("切换成功") : R.failed("切换失败");
        } catch (Exception e) {
            log.error("user_switchStatus_error:{}", e);
            return R.failed(e.getMessage());
        }
    }

    /**
     * 用户角色授权
     *
     * @param paramDto
     * @return
     */
    @PostMapping("/user/grant/role")
    public R<?> grantRole(@RequestBody UserGrantRoleParamDto paramDto) {
        try {
            Assert.notNull(paramDto.getUserId(), "[用户不能为空]");
            userRoleService.remove(Wrappers.lambdaQuery(UserRole.class).eq(UserRole::getUserId, paramDto.getUserId()));
            if (CollUtil.isNotEmpty(paramDto.getRoleIds())) {
                List<UserRole> urs = paramDto.getRoleIds().stream().map(roleId -> {
                    return new UserRole()
                            .setId(String.valueOf(System.nanoTime()))
                            .setUserId(paramDto.getUserId())
                            .setRoleId(roleId)
                            .setCreateTime(new Date())
                            .setCreateUser(SecureUtil.currentUserId());
                }).collect(Collectors.toList());
                userRoleService.grantRole(urs);
            }
            return R.ok("授权成功");
        } catch (Exception e) {
            log.error("user_grant_role_error:{}", e);
            return R.failed(e.getMessage());
        }
    }

    /**
     * 通过token查询用户详情信息
     *
     * @return
     */
    @GetMapping("/user/getUserInfo")
    public R<?> getUserInfo() {
        try {
            String userId = SecureUtil.currentUserId();
            if (StringUtils.isBlank(userId)) {
                return R.failed("账户信息已失效，请重新登录");
            }
            User user = userService.getById(userId);
            user.setPassword(null);
            List<Role> roles = userService.getUser2roleList(userId);
            List<Menu> menus = userService.getUser2menuList(user.getId());
            if (null != roles && CollUtil.isNotEmpty(roles)) {
                user.setRoles(roles).setRoleIds(roles.stream().map(Role::getId).distinct().collect(Collectors.toList()));
            }
            if (null != menus && CollUtil.isNotEmpty(menus)) {
                user.setMenus(MenuService.buildTree(menus));
            }
            return R.ok(user);
        } catch (Exception e) {
            log.error("user_getUserInfo_error:{}", e);
            return R.failed(e.getMessage());
        }
    }

    /**
     * 新增角色
     *
     * @param role
     * @return
     */
    @PostMapping("/role/save")
    public R<?> saveRole(@RequestBody Role role) {
        try {
            Assert.notNull(role, "[用户不能为空]");
            Assert.notNull(role.getName(), "[名称不能为空]");
            role.setId(String.valueOf(System.nanoTime()))
                    .setCreateTime(new Date())
                    .setCreateUser(SecureUtil.currentUserId())
                    .setUpdateUser(SecureUtil.currentUserId())
                    .setUpdateTime(new Date());
            Boolean flag = roleService.save(role);
            return flag ? R.ok("创建成功") : R.failed("创建失败");
        } catch (Exception e) {
            log.error("role_save_error:{}", e);
            return R.failed(e.getMessage());
        }
    }

    /**
     * 编辑角色
     *
     * @param role
     */
    @PostMapping("/role/update")
    public R<?> updateRole(@RequestBody Role role) {
        try {
            Assert.notNull(role.getId(), "[id不能为空]");
            Assert.notNull(role.getName(), "[名称不能为空]");
            Boolean flag = roleService.update(Wrappers.lambdaUpdate(Role.class)
                    .eq(Role::getId, role.getId())
                    .set(Role::getName, role.getName())
                    .set(Role::getRemark, role.getRemark())
                    .set(Role::getUpdateTime, new Date())
                    .set(Role::getUpdateUser, SecureUtil.currentUserId())
            );
            return flag ? R.ok("编辑成功") : R.failed("编辑失败");
        } catch (Exception e) {
            log.error("role_update_error:{}", e);
            return R.failed(e.getMessage());
        }
    }

    /**
     * 删除角色
     *
     * @param role
     * @return
     */
    @DeleteMapping("/role/del")
    public R<?> delRole(@RequestBody Role role) {
        try {
            Assert.notNull(role.getId(), "[id不能为空]");
            List<User> users = roleService.getRole2userList(role.getId());
            if (CollUtil.isNotEmpty(users)) {
                return R.failed("该角色在使用中,不允许删除");
            }
            roleService.removeById(role.getId());
            userRoleService.remove(Wrappers.lambdaQuery(UserRole.class).eq(UserRole::getRoleId, role.getId()));
            return R.ok("删除成功");
        } catch (Exception e) {
            log.error("role_del_error:{}", e);
            return R.failed(e.getMessage());
        }
    }

    /**
     * 角色全量列表
     *
     * @param name 角色名称
     * @return
     */
    @GetMapping("/role/list")
    public R<?> roleList(String name) {
        try {
            LambdaQueryWrapper<Role> qw = new LambdaQueryWrapper<>();
            qw.like(StringUtils.isNotBlank(name), Role::getName, name)
                    .orderByDesc(Role::getCreateTime);
            return R.ok(roleService.list(qw));
        } catch (Exception e) {
            log.error("role_list_error:{}", e);
            return R.failed(e.getMessage());
        }
    }

    /**
     * 角色分页列表
     *
     * @param pageNo          当前页数
     * @param pageSize        每页数量
     * @param name            角色名称
     * @param startDate       开始时间
     * @param endDate         结束时间
     * @param startUpdateDate 开始更新时间
     * @param endUpdateDate   结束更新时间
     * @return
     */
    @GetMapping("/role/page")
    public R<?> rolePage(@RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                         @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                         String name,
                         String startDate,
                         String endDate,
                         String startUpdateDate,
                         String endUpdateDate) {
        try {
            LambdaQueryWrapper<Role> qw = new LambdaQueryWrapper<>();
            qw.between((StringUtils.isNotBlank(startDate) && StringUtils.isNotBlank(endDate)), Role::getCreateTime, startDate + " 00:00:00", endDate + " 23:59:59")
                    .between((StringUtils.isNotBlank(startUpdateDate) && StringUtils.isNotBlank(endUpdateDate)), Role::getUpdateTime, startUpdateDate + " 00:00:00", endUpdateDate + " 23:59:59")
                    .like(StringUtils.isNotBlank(name), Role::getName, name)
                    .orderByDesc(Role::getCreateTime);
            Map<String, String> um = userService.list(Wrappers.lambdaQuery(User.class).select(User::getId, User::getName)).stream().collect(Collectors.toMap(User::getId, User::getName, (n1, n2) -> n1));
            Page<Role> rolePage = roleService.page(new Page<>(pageNo, pageSize), qw);
            rolePage.getRecords().forEach(role -> {
                if (StringUtils.isNotBlank(role.getCreateUser()) && um.containsKey(role.getCreateUser())) {
                    role.setCreateUser(um.get(role.getCreateUser()));
                }
                if (StringUtils.isNotBlank(role.getUpdateUser()) && um.containsKey(role.getUpdateUser())) {
                    role.setUpdateUser(um.get(role.getUpdateUser()));
                }
            });
            return R.ok(rolePage);
        } catch (Exception e) {
            log.error("role_page_error:{}", e);
            return R.failed(e.getMessage());
        }
    }

    /**
     * 角色详情
     *
     * @param roleId
     * @return
     */
    @GetMapping("/role/detail")
    public R<?> roleDetail(@RequestParam("roleId") String roleId) {
        try {
            Assert.notNull(roleId, "[id不能为空]");
            Role role = roleService.getById(roleId);
            List<User> users = roleService.getRole2userList(roleId).stream().peek(u -> u.setPassword(null)).collect(Collectors.toList());
            if (null != users && CollUtil.isNotEmpty(users)) {
                role.setUsers(users);
                role.setUserIds(users.stream().map(User::getId).distinct().collect(Collectors.toList()));
            }
            List<Menu> menus = roleService.getRole2menuList(roleId);
            if (null != menus && CollUtil.isNotEmpty(menus)) {
                role.setMenus(menus);
                role.setMenuIds(menus.stream().map(Menu::getId).distinct().collect(Collectors.toList()));
            }
            return R.ok(role);
        } catch (Exception e) {
            log.error("role_detail_error:{}", e);
            return R.failed(e.getMessage());
        }
    }

    /**
     * 角色菜单授权
     *
     * @param paramDto
     * @return
     */
    @PostMapping("/role/grant/menu")
    public R<?> grantMenu(@RequestBody RoleGrantMenuParamDto paramDto) {
        try {
            Assert.notNull(paramDto.getRoleId(), "[角色不能为空]");
            roleMenuService.remove(Wrappers.lambdaQuery(RoleMenu.class).eq(RoleMenu::getRoleId, paramDto.getRoleId()));
            if (CollUtil.isNotEmpty(paramDto.getMenuIds())) {
                List<RoleMenu> rms = paramDto.getMenuIds().stream().map(menuId -> {
                    return new RoleMenu()
                            .setId(String.valueOf(System.nanoTime()))
                            .setRoleId(paramDto.getRoleId())
                            .setMenuId(menuId)
                            .setCreateTime(new Date())
                            .setCreateUser(SecureUtil.currentUserId());
                }).collect(Collectors.toList());
                roleMenuService.grantMenu(rms);
            }
            return R.ok("授权成功");
        } catch (Exception e) {
            log.error("role_grant_menu_error:{}", e);
            return R.failed(e.getMessage());
        }
    }

    /**
     * 角色绑定用户
     *
     * @param paramDto
     * @return
     */
    @PostMapping("/role/grant/user")
    public R<?> grantRole(@RequestBody RoleGrantUserParamDto paramDto) {
        try {
            Assert.notNull(paramDto.getRoleId(), "[角色不能为空]");
            userRoleService.remove(Wrappers.lambdaQuery(UserRole.class).eq(UserRole::getRoleId, paramDto.getRoleId()));
            if (CollUtil.isNotEmpty(paramDto.getUserIds())) {
                List<UserRole> urs = paramDto.getUserIds().stream().map(userId -> {
                    return new UserRole()
                            .setId(String.valueOf(System.nanoTime()))
                            .setUserId(userId)
                            .setRoleId(paramDto.getRoleId())
                            .setCreateTime(new Date())
                            .setCreateUser(SecureUtil.currentUserId());
                }).collect(Collectors.toList());
                userRoleService.grantRole(urs);
            }
            return R.ok("授权成功");
        } catch (Exception e) {
            log.error("role_grant_user_error:{}", e);
            return R.failed(e.getMessage());
        }
    }

    /**
     * 新建菜单
     *
     * @param menu
     * @return
     */
    @PostMapping("/menu/save")
    public R<?> saveMenu(@RequestBody Menu menu) {
        try {
            Assert.notNull(menu, "[菜单不能为空]");
            Assert.notNull(menu.getName(), "[菜单名称不能为空]");
            Assert.notNull(menu.getType(), "[菜单类型不能为空]");
            Assert.notNull(menu.getIsFrame(), "[是否外链不能为空]");
            Assert.notNull(menu.getComponent(), "[组件路径不能为空]");
            Assert.notNull(menu.getPath(), "[路由地址不能为空]");
            Assert.notNull(menu.getIsCache(), "[是否缓存不能为空]");
            menu.setId(String.valueOf(System.nanoTime()))
                    .setCreateTime(new Date())
                    .setCreateUser(SecureUtil.currentUserId());
            Boolean flag = menuService.save(menu);
            return flag ? R.ok("创建成功") : R.failed("创建失败");
        } catch (Exception e) {
            log.error("menu_save_error:{}", e);
            return R.failed(e.getMessage());
        }
    }

    /**
     * 菜单全量列表
     *
     * @param name 用户名称
     * @return
     */
    @GetMapping("/menu/list")
    public R<?> menuList(String name) {
        try {
            LambdaQueryWrapper<Menu> qw = new LambdaQueryWrapper<>();
            qw.like(StringUtils.isNotBlank(name), Menu::getName, name)
                    .orderByDesc(Menu::getCreateTime);
            return R.ok(menuService.list(qw));
        } catch (Exception e) {
            log.error("menu_list_error:{}", e);
            return R.failed(e.getMessage());
        }
    }

    /**
     * 菜单分页列表
     *
     * @param pageNo   当前页数
     * @param pageSize 每页大小
     * @param name     用户名称
     * @return
     */
    @GetMapping("/menu/page")
    public R<?> menuPage(@RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                         @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                         String name) {
        try {
            LambdaQueryWrapper<Menu> qw = new LambdaQueryWrapper<>();
            qw.like(StringUtils.isNotBlank(name), Menu::getName, name)
                    .orderByDesc(Menu::getCreateTime);
            return R.ok(menuService.page(new Page<>(pageNo, pageSize), qw));
        } catch (Exception e) {
            log.error("menu_page_error:{}", e);
            return R.failed(e.getMessage());
        }
    }

    /**
     * 删除菜单
     *
     * @param menu
     * @return
     */
    @DeleteMapping("/menu/del")
    public R<?> delMenu(@RequestBody Menu menu) {
        try {
            Assert.notNull(menu.getId(), "[id不能为空]");
            menuService.removeById(menu.getId());
            roleMenuService.remove(Wrappers.lambdaQuery(RoleMenu.class).eq(RoleMenu::getMenuId, menu.getId()));
            return R.ok("删除成功");
        } catch (Exception e) {
            log.error("menu_del_error:{}", e);
            return R.failed(e.getMessage());
        }
    }

    /**
     * 菜单树状数据
     *
     * @param name
     * @return
     */
    @GetMapping("/menu/tree")
    public R<?> menuTree(String name) {
        try {
            LambdaQueryWrapper<Menu> qw = new LambdaQueryWrapper<>();
            qw.like(StringUtils.isNotBlank(name), Menu::getName, name)
                    .orderByDesc(Menu::getCreateTime);
            Map<String, String> um = userService.list(Wrappers.lambdaQuery(User.class).select(User::getId, User::getName)).stream().collect(Collectors.toMap(User::getId, User::getName, (n1, n2) -> n1));
            List<Menu> menus = menuService.list(qw);
            menus.forEach(menu -> {
                if (StringUtils.isNotBlank(menu.getCreateUser()) && um.containsKey(menu.getCreateUser())) {
                    menu.setCreateUser(um.get(menu.getCreateUser()));
                }
                if (StringUtils.isNotBlank(menu.getUpdateUser()) && um.containsKey(menu.getUpdateUser())) {
                    menu.setUpdateUser(um.get(menu.getUpdateUser()));
                }
            });
            return R.ok(MenuService.buildTree(menus));
        } catch (Exception e) {
            log.error("menu_tree_error:{}", e);
            return R.failed(e.getMessage());
        }
    }

    /**
     * 编辑菜单
     *
     * @param menu
     */
    @PostMapping("/menu/update")
    public R<?> updateMenu(@RequestBody Menu menu) {
        try {
            Assert.notNull(menu, "[菜单不能为空]");
            Assert.notNull(menu.getName(), "[菜单名称不能为空]");
            Assert.notNull(menu.getType(), "[菜单类型不能为空]");
            Assert.notNull(menu.getIsFrame(), "[是否外链不能为空]");
            Assert.notNull(menu.getComponent(), "[组件路径不能为空]");
            Assert.notNull(menu.getPath(), "[路由地址不能为空]");
            Assert.notNull(menu.getIsCache(), "[是否缓存不能为空]");
            menu.setUpdateTime(new Date()).setUpdateUser(SecureUtil.currentUserId());
            Boolean flag = menuService.update(menu, Wrappers.lambdaUpdate(Menu.class).eq(Menu::getId, menu.getId()));
            return flag ? R.ok("编辑成功") : R.failed("编辑失败");
        } catch (Exception e) {
            log.error("menu_update_error:{}", e);
            return R.failed(e.getMessage());
        }
    }

    /**
     * 切换菜单显示状态 1:显示，0:隐藏
     *
     * @param menu
     * @return
     */
    @PostMapping("/menu/switchVisible")
    public R<?> switchVisible(@RequestBody Menu menu) {
        try {
            Assert.notNull(menu.getId(), "[id不能为空]");
            Assert.notNull(menu.getVisible(), "[显示状态不能为空]");
            Boolean flag = menuService.update(Wrappers.lambdaUpdate(Menu.class)
                    .eq(Menu::getId, menu.getId())
                    .set(Menu::getVisible, menu.getVisible())
                    .set(Menu::getUpdateTime, new Date())
                    .set(Menu::getUpdateUser, SecureUtil.currentUserId())
            );
            return flag ? R.ok("切换成功") : R.failed("切换失败");
        } catch (Exception e) {
            log.error("menu_switchVisible_error:{}", e);
            return R.failed(e.getMessage());
        }
    }

    /**
     * 用户修改密码
     *
     * @param dto
     * @return
     */
    @PostMapping("/user/updatePwd")
    public R<Object> modifyPwd(@RequestBody PwdDto dto) {
        try {
            if (StringUtils.isBlank(dto.getId())) {
                dto.setId(SecureUtil.currentUserId());
            }
            if (StringUtils.isBlank(dto.getId())) {
                return R.restResult(null, 400, "参数异常");
            }
            if (StringUtils.isBlank(dto.getOldPassword())) {
                return R.restResult(null, 400, "原密码缺失");
            }
            if (StringUtils.isBlank(dto.getNewPassword())) {
                return R.restResult(null, 400, "新密码缺失");
            }
            if ((int) userService.count(Wrappers.lambdaQuery(User.class).eq(User::getId, dto.getId()).eq(User::getAdFlag, 1)) > 0) {
                return R.restResult(null, 401, "不允许修改密码");
            }
            LambdaQueryWrapper<User> qw = new LambdaQueryWrapper<>();
            qw.eq(User::getId, dto.getId())
                    .eq(User::getPassword, MD5.create().digestHex(encryptionUtil.decrypt(dto.getOldPassword())));
            if (((int) userService.count(qw)) <= 0) {
                return R.restResult(null, 400, "原密码错误");
            }
            LambdaUpdateWrapper<User> uw = new LambdaUpdateWrapper<>();
            uw.eq(User::getId, dto.getId())
                    .set(User::getPassword, MD5.create().digestHex(encryptionUtil.decrypt(dto.getNewPassword())));
            Boolean flag = userService.update(uw);
            return flag ? R.ok("修改成功") : R.failed("修改失败");
        } catch (Exception e) {
            log.error("user_updatePwd_error:{}", e);
            return R.failed(e.getMessage());
        }

    }
}
