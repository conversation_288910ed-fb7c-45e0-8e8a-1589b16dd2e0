package cn.sdata.om.al.controller;

import cn.hutool.core.date.DateUtil;
import cn.sdata.om.al.constant.BaseConstant;
import cn.sdata.om.al.entity.MarketTradeDay;
import cn.sdata.om.al.result.R;
import cn.sdata.om.al.service.MarketTradeDayService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Map;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("trade-day")
@Slf4j
public class MarketTradeDayController {

    @Resource
    private MarketTradeDayService marketTradeDayService;

    /**
     * 通过市场获取交易日
     *
     * @param baseDate      日期
     * @param tradeType 交易类型
     * @return 交易日信息
     */
    @GetMapping(value = "get")
    public R<String> getTradeDay(String baseDate, String tradeType, int offset) {
        String netValueTradeDay = marketTradeDayService.getNetValueTradeDay(baseDate, tradeType, offset);
        return R.ok(netValueTradeDay, "");
    }

    @GetMapping(value = "between")
    public R<Map<String, String>> getBetweenTradeDay(String baseDate, String tradeType, int offset) {
        Map<String, String> netValueBetweenTradeDay = marketTradeDayService.getNetValueBetweenTradeDay(baseDate, tradeType, offset);
        return R.ok(netValueBetweenTradeDay);
    }
}
