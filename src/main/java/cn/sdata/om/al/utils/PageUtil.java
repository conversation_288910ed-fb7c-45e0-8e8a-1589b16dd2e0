package cn.sdata.om.al.utils;

import cn.hutool.core.collection.CollectionUtil;
import com.github.pagehelper.PageInfo;

import java.util.ArrayList;
import java.util.List;

public class PageUtil {

    /**
     * 组装分页信息
     *
     * @param queryList 查询的结果
     * @param total     总数
     * @param pageNo    第几页
     * @param pageSize  每页几条
     * @param <T>       泛型
     * @return 分页对象
     */
    public static <T> PageInfo<T> handleRightPage(List<T> queryList, int total, int pageNo, int pageSize) {
        PageInfo<T> pageInfo = new PageInfo<>();
        pageInfo.setTotal(total);
        pageInfo.setList(CollectionUtil.isNotEmpty(queryList) ? queryList : new ArrayList<>());
        if (total % pageSize == 0) {
            pageInfo.setPages(total / pageSize);
        } else {
            pageInfo.setPages(total / pageSize + 1);
        }
        pageInfo.setPageNum(pageNo);
        pageInfo.setPageSize(pageSize);
        int pages = pageInfo.getPages();
        pageInfo.setIsLastPage(pageNo == pages || pages == 0);
        return pageInfo;
    }
}
