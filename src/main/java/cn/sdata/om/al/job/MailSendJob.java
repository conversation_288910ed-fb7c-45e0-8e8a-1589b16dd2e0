package cn.sdata.om.al.job;

import cn.sdata.om.al.constant.BaseConstant;
import cn.sdata.om.al.entity.*;
import cn.sdata.om.al.enums.DisclosureMethod;
import cn.sdata.om.al.enums.MailContentHandler;
import cn.sdata.om.al.enums.MailStatus;
import cn.sdata.om.al.qrtz.QuartzJobBean;
import cn.sdata.om.al.qrtz.QuartzJobFactory;
import cn.sdata.om.al.qrtz.constant.CronConstant;
import cn.sdata.om.al.qrtz.entity.Cron;
import cn.sdata.om.al.qrtz.service.CronService;
import cn.sdata.om.al.service.LogCustodianRecordsService;
import cn.sdata.om.al.service.LogMailSendRecordsService;
import cn.sdata.om.al.service.NetValueDisclosureService;
import cn.sdata.om.al.service.mail.MailInfoService;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.quartz.JobDataMap;
import org.quartz.JobExecutionContext;
import org.springframework.aop.framework.AopContext;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

import static cn.sdata.om.al.constant.JobConstant.*;

@Slf4j
@Component
@AllArgsConstructor
public class MailSendJob implements QuartzJobBean {

    private final MailInfoService mailInfoService;
    private final CronService cronService;
    private final NetValueDisclosureService netValueDisclosureService;
    private final LogMailSendRecordsService logMailSendRecordsService;
    private final LogCustodianRecordsService logCustodianRecordsService;


    @Override
    @SuppressWarnings("unchecked")
    public void doExecute(JobExecutionContext context) {
        Objects.requireNonNull(context, "JobExecutionContext is null");
        JobDataMap mergedJobDataMap = context.getMergedJobDataMap();
        String jobId = (String) mergedJobDataMap.get(CronConstant.JOB_ID);
        String dataDate = (String) mergedJobDataMap.get(DATA_DATE);
        Map<String, RemoteFileInfo> infoMap = (Map<String, RemoteFileInfo>) mergedJobDataMap.get(REMOTE_FILE);
        List<NetValueDisclosure> list = (List<NetValueDisclosure>) mergedJobDataMap.get(NET_VALUE_DISCLOSURE_LIST);
        if (list != null && !list.isEmpty()) {
            Map<String, NetValueDisclosure> productIdNetValue = list.stream().collect(Collectors.toMap(NetValueDisclosure::getProductId,
                    netValueDisclosure -> netValueDisclosure,
                    (oldOne, newOne) -> newOne));
            log.info("查看MailSendJob中的数据状态:{}", list);
            List<String> productIds = (List<String>) mergedJobDataMap.get(PRODUCT_ID);
            Cron cron = cronService.getById(jobId);
            List<SendMailInfo> sendMailInfos = mailInfoService.composeByConfig(cron, dataDate, new LinkedHashSet<>(productIds), infoMap);
            for (SendMailInfo sendMailInfo : sendMailInfos) {
                List<String> mailProductIds = sendMailInfo.getProductIds();
                for (String mailProductId : mailProductIds) {
                    NetValueDisclosure netValueDisclosure = productIdNetValue.get(mailProductId);
                    if (netValueDisclosure != null) {
                        dealMailStatus(netValueDisclosure, cron);
                    }
                }
            }
            //这里只更新邮件相关的字段,不能全部更新以免其他状态被覆盖
//            netValueDisclosureService.saveOrUpdateBatch(list);
            log.info("开始邮件发送:{}", sendMailInfos);
            sendMailInfos = mailInfoService.doSendMailInfo(sendMailInfos);
            log.info("结束邮件发送:{}", sendMailInfos);
            List<LogCustodianRecords> logCustodianRecordsList = new ArrayList<>();
            List<LogMailSendRecords> logMailSendRecordsList = new ArrayList<>();
            for (SendMailInfo sendMailInfo : sendMailInfos) {
                List<String> mailProductIds = sendMailInfo.getProductIds();
                for (String mailProductId : mailProductIds) {
                    NetValueDisclosure netValueDisclosure = productIdNetValue.get(mailProductId);
                    if (netValueDisclosure != null) {
                        dealNetValueDisclosure(netValueDisclosure, sendMailInfo, cron, logCustodianRecordsList, logMailSendRecordsList);
                    }
                }
            }
//            netValueDisclosureService.saveOrUpdateBatch(list);
            log.info("查看MailSendJob结束时的数据状态:{}", list);
            if (!logCustodianRecordsList.isEmpty()) {
                logCustodianRecordsService.saveBatch(logCustodianRecordsList);
            }
            if (!logMailSendRecordsList.isEmpty()) {
                logMailSendRecordsService.saveBatch(logMailSendRecordsList);
            }
        }
    }

    private void dealMailStatus(NetValueDisclosure netValueDisclosure, Cron cron){
        Objects.requireNonNull(netValueDisclosure, "净值披露不得为空");
        Objects.requireNonNull(cron, "调度任务不得为空");
        String jobName = cron.getClassName();
        LambdaUpdateWrapper<NetValueDisclosure> netValueDisclosureUpdateWrapper = new LambdaUpdateWrapper<>();
        netValueDisclosureUpdateWrapper.eq(NetValueDisclosure::getId, netValueDisclosure.getId());
        log.info("处理邮件时的状态:{}", netValueDisclosure);
        switch (jobName){
            case "CustodianBankMailSendJob":
                String custodyReconciliationEmailSent = netValueDisclosure.getCustodyReconciliationEmailSent();
                if (!MailStatus.NONE.name().equals(custodyReconciliationEmailSent)) {
                    netValueDisclosure.setCustodyReconciliationEmailSent(MailStatus.SENDING.name());
                    netValueDisclosureUpdateWrapper.set(NetValueDisclosure::getCustodyReconciliationEmailSent, MailStatus.SENDING.name());
                }
                break;
            case "InvestorMailSendJob":
                netValueDisclosure.setValuationTableSent(MailStatus.SENDING.name());
                netValueDisclosureUpdateWrapper.set(NetValueDisclosure::getValuationTableSent, MailStatus.SENDING.name());
                break;
            case "ThirdMailSendJob":
                netValueDisclosure.setThirdPartySent(MailStatus.SENDING.name());
                netValueDisclosureUpdateWrapper.set(NetValueDisclosure::getThirdPartySent, MailStatus.SENDING.name());
                break;
            case "InvestorStatementMailSendJob":
                netValueDisclosure.setInvestorReportSent(MailStatus.SENDING.name());
                netValueDisclosureUpdateWrapper.set(NetValueDisclosure::getInvestorReportSent, MailStatus.SENDING.name());
                break;
            case "NetValueDisclosureMailSendJob":
                netValueDisclosure.setNetValueDisclosed(2);
                netValueDisclosureUpdateWrapper.set(NetValueDisclosure::getNetValueDisclosed, 2);
                break;
        }
        netValueDisclosureService.update(netValueDisclosureUpdateWrapper);
    }

    private void dealNetValueDisclosure(NetValueDisclosure netValueDisclosure, SendMailInfo sendMailInfo, Cron cron, List<LogCustodianRecords> logCustodianRecordsList, List<LogMailSendRecords> logMailSendRecordsList){
        Objects.requireNonNull(netValueDisclosure, "净值披露不得为空");
        Objects.requireNonNull(cron, "调度任务不得为空");
        log.info("邮件发送完成时的状态:{}", netValueDisclosure);
        LambdaUpdateWrapper<NetValueDisclosure> netValueDisclosureUpdateWrapper = new LambdaUpdateWrapper<>();
        netValueDisclosureUpdateWrapper.eq(NetValueDisclosure::getId, netValueDisclosure.getId());
        String jobName = cron.getClassName();
        switch (jobName){
            case "CustodianBankMailSendJob":
                String custodyReconciliationEmailSent = netValueDisclosure.getCustodyReconciliationEmailSent();
                if (!MailStatus.NONE.name().equals(custodyReconciliationEmailSent)) {
                    netValueDisclosure.setCustodyReconciliationEmailSent(sendMailInfo.getStatus());
                    netValueDisclosureUpdateWrapper.set(NetValueDisclosure::getCustodyReconciliationEmailSent, sendMailInfo.getStatus());
                }
                LogCustodianRecords logCustodianRecords = composeCustodianLog(sendMailInfo, netValueDisclosure);
                logCustodianRecordsList.add(logCustodianRecords);
                break;
            case "InvestorMailSendJob":
                netValueDisclosure.setValuationTableSent(sendMailInfo.getStatus());
                netValueDisclosureUpdateWrapper.set(NetValueDisclosure::getValuationTableSent, sendMailInfo.getStatus());
                LogMailSendRecords logMailSendRecords = composeMailSendLog(sendMailInfo, netValueDisclosure);
                logMailSendRecordsList.add(logMailSendRecords);
                break;
            case "NetValueDisclosureMailSendJob":
                netValueDisclosure.setNetValueDisclosed(1);
                netValueDisclosureUpdateWrapper.set(NetValueDisclosure::getNetValueDisclosed, 1);
                LogMailSendRecords netLogMailSendRecords = composeMailSendLog(sendMailInfo, netValueDisclosure);
                logMailSendRecordsList.add(netLogMailSendRecords);
                break;
            case "ThirdMailSendJob":
                netValueDisclosure.setThirdPartySent(sendMailInfo.getStatus());
                netValueDisclosureUpdateWrapper.set(NetValueDisclosure::getThirdPartySent, sendMailInfo.getStatus());
                LogMailSendRecords logMailSendRecordsThird = composeMailSendLog(sendMailInfo, netValueDisclosure);
                logMailSendRecordsList.add(logMailSendRecordsThird);
                break;
            case "InvestorStatementMailSendJob":
                netValueDisclosure.setInvestorReportSent(sendMailInfo.getStatus());
                netValueDisclosureUpdateWrapper.set(NetValueDisclosure::getInvestorReportSent, sendMailInfo.getStatus());
                LogMailSendRecords logMailSendRecordsReport = composeMailSendLog(sendMailInfo, netValueDisclosure);
                logMailSendRecordsList.add(logMailSendRecordsReport);
                break;
        }
        netValueDisclosureService.update(netValueDisclosureUpdateWrapper);
    }

    private LogCustodianRecords composeCustodianLog(SendMailInfo sendMailInfo, NetValueDisclosure netValueDisclosure){
        Objects.requireNonNull(sendMailInfo,"邮件信息为空");
        Objects.requireNonNull(netValueDisclosure,"邮件信息为空");
        LogCustodianRecords logCustodianRecords = new LogCustodianRecords();
        logCustodianRecords.setMailSendTime(new Date());
        logCustodianRecords.setProductId(netValueDisclosure.getProductId());
        logCustodianRecords.setValuationDate(netValueDisclosure.getValuationDate());
        logCustodianRecords.setCustodianStatus(sendMailInfo.getStatus());
        logCustodianRecords.setCustodianOperator(BaseConstant.DEFAULT_USERNAME);
        logCustodianRecords.setMailId(sendMailInfo.getLogId());
        return logCustodianRecords;
    }

    private LogMailSendRecords composeMailSendLog(SendMailInfo sendMailInfo, NetValueDisclosure netValueDisclosure){
        Objects.requireNonNull(sendMailInfo, "邮件信息为空");
        LogMailSendRecords logMailSendRecords = new LogMailSendRecords();
        logMailSendRecords.setProductId(netValueDisclosure.getProductId());
        logMailSendRecords.setValuationDate(netValueDisclosure.getValuationDate());
        logMailSendRecords.setSendMethod(DisclosureMethod.MAIL.name());
        MailContentHandler mailContentHandler = MailContentHandler.valueOf(sendMailInfo.getHandler());
        logMailSendRecords.setSendContent(mailContentHandler.name());
        logMailSendRecords.setSendTo(sendMailInfo.getContactName());
        logMailSendRecords.setSendTime(new Date());
        logMailSendRecords.setMailId(sendMailInfo.getLogId());
        logMailSendRecords.setStatus(sendMailInfo.getStatus());
        logMailSendRecords.setContactType(sendMailInfo.getContactType());
        return logMailSendRecords;
    }

    @Override
    public void execute(JobExecutionContext context) {
        if (context != null) {
            log.info("开始执行邮件发送");
            MailSendJob mailSendJob = (MailSendJob) AopContext.currentProxy();
            mailSendJob.doExecute(context);
        }else {
            log.error("无JobExecutionContext");
        }
    }

    @Override
    public void afterPropertiesSet() {
        QuartzJobFactory.addJobClass("MailSendJob", MailSendJob.class);
    }
}
