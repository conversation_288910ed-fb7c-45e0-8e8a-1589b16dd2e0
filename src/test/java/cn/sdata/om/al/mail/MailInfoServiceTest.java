package cn.sdata.om.al.mail;

import cn.hutool.core.collection.CollectionUtil;
import cn.sdata.om.al.config.EncryptionConfig;
import cn.sdata.om.al.constant.BaseConstant;
import cn.sdata.om.al.constant.JobConstant;
import cn.sdata.om.al.entity.*;
import cn.sdata.om.al.enums.CustodianType;
import cn.sdata.om.al.enums.DisclosureMethod;
import cn.sdata.om.al.qrtz.entity.Cron;
import cn.sdata.om.al.qrtz.service.CronService;
import cn.sdata.om.al.rpa.BaseHandler;
import cn.sdata.om.al.rpa.ValuationTableHandler;
import cn.sdata.om.al.service.*;
import cn.sdata.om.al.service.mail.MailInfoService;
import cn.sdata.om.al.utils.MailUtil;
import cn.sdata.om.al.utils.StringUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.util.NumberToTextConverter;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.jetbrains.annotations.NotNull;
import org.junit.jupiter.api.Test;
import org.quartz.JobDataMap;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.ApplicationContext;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;

import java.io.FileOutputStream;
import java.io.InputStream;
import java.util.*;
import java.util.stream.Collectors;

import static cn.sdata.om.al.enums.MailContentHandler.*;

@SpringBootTest
@Slf4j
public class MailInfoServiceTest {

    private MailInfoService mailInfoService;
    private MailUtil mailUtil;
    private CronService cronService;
    private AccountInformationService accountInformationService;
    private CustodianBankContactsViewService custodianBankContactsViewService;
    private CustodianBankContactsService custodianBankContactsService;
    private InvestorContactsViewService investorContactsViewService;
    private InvestorContactsService investorContactsService;
    private ApplicationContext applicationContext;
    private NetValueDisclosureService netValueDisclosureService;
    private ResourceLoader resourceLoader;
    @Autowired
    public void setMailInfoService(MailInfoService mailInfoService) {
        this.mailInfoService = mailInfoService;
    }
    @Autowired
    public void setMailUtil(MailUtil mailUtil) {
        this.mailUtil = mailUtil;
    }
    @Autowired
    public void setCronService(CronService cronService) {
        this.cronService = cronService;
    }
    @Autowired
    public void setAccountInformationService(AccountInformationService accountInformationService) {
        this.accountInformationService = accountInformationService;
    }
    @Autowired
    public void setCustodianBankContactsViewService(CustodianBankContactsViewService custodianBankContactsViewService) {
        this.custodianBankContactsViewService = custodianBankContactsViewService;
    }
    @Autowired
    public void setCustodianBankContactsService(CustodianBankContactsService custodianBankContactsService) {
        this.custodianBankContactsService = custodianBankContactsService;
    }

    @Autowired
    public void setApplicationContext(ApplicationContext applicationContext) {
        this.applicationContext = applicationContext;
    }
    @Autowired
    public void setNetValueDisclosureService(NetValueDisclosureService netValueDisclosureService) {
        this.netValueDisclosureService = netValueDisclosureService;
    }
    @Autowired
    public void setResourceLoader(ResourceLoader resourceLoader) {
        this.resourceLoader = resourceLoader;
    }
    @Autowired
    public void setInvestorContactsViewService(InvestorContactsViewService investorContactsViewService) {
        this.investorContactsViewService = investorContactsViewService;
    }
    @Autowired
    public void setInvestorContactsService(InvestorContactsService investorContactsService) {
        this.investorContactsService = investorContactsService;
    }

    /**
     * 测试发送邮件完整流程
     */
    @Test
    public void testInvestor() {
        System.setProperty("mail.mime.splitlongparameters", "false");
        Cron cron = cronService.getById("1897567486157611010");
        Map<String, RemoteFileInfo> hashMap = new HashMap<>();
//        hashMap.put("2101", new RemoteFileInfo
//                ("", 0, "证券投资基金估值表_分红PAR_I_自有建行_2024-11-01.xls",
//                        "\\研发\\安联\\估值表", new Date(), new Date(), 1L, "1",
//                        "\\研发\\安联\\估值表"));
//        hashMap.put("2102", new RemoteFileInfo
//                ("", 0, "证券投资基金估值表_分红PAR_III_自有建行_2024-11-01.xls",
//                        "\\研发\\安联\\估值表", new Date(), new Date(), 1L, "1",
//                        "\\研发\\安联\\估值表"));
//        hashMap.put("2103", new RemoteFileInfo
//                ("", 0, "证券投资基金估值表_分红PAR_IV_自有建行_2024-11-01.xls",
//                        "\\研发\\安联\\估值表", new Date(), new Date(), 1L, "1",
//                        "\\研发\\安联\\估值表"));
//        hashMap.put("2104", new RemoteFileInfo
//                ("", 0, "证券投资基金估值表_分红PAR_V_自有建行_2024-11-01.xls",
//                        "\\研发\\安联\\估值表", new Date(), new Date(), 1L, "1",
//                        "\\研发\\安联\\估值表"));
        mailInfoService.doSendMailInfo(cron, "2025-03-06", hashMap.keySet(), hashMap);
    }

    @Test
    public void testNetMail() {
        System.setProperty("mail.mime.splitlongparameters", "false");
        Cron cron = cronService.getById("1897567486157611010");
        Map<String, RemoteFileInfo> hashMap = new HashMap<>();
//        hashMap.put("2101", new RemoteFileInfo
//                ("", 0, "证券投资基金估值表_分红PAR_I_自有建行_2024-11-01.xls",
//                        "\\研发\\安联\\估值表", new Date(), new Date(), 1L, "1",
//                        "\\研发\\安联\\估值表"));
//        hashMap.put("2102", new RemoteFileInfo
//                ("", 0, "证券投资基金估值表_分红PAR_III_自有建行_2024-11-01.xls",
//                        "\\研发\\安联\\估值表", new Date(), new Date(), 1L, "1",
//                        "\\研发\\安联\\估值表"));
//        hashMap.put("2103", new RemoteFileInfo
//                ("", 0, "证券投资基金估值表_分红PAR_IV_自有建行_2024-11-01.xls",
//                        "\\研发\\安联\\估值表", new Date(), new Date(), 1L, "1",
//                        "\\研发\\安联\\估值表"));
//        hashMap.put("2104", new RemoteFileInfo
//                ("", 0, "证券投资基金估值表_分红PAR_V_自有建行_2024-11-01.xls",
//                        "\\研发\\安联\\估值表", new Date(), new Date(), 1L, "1",
//                        "\\研发\\安联\\估值表"));
        mailInfoService.doSendMailInfo(cron, "2024-11-29", hashMap.keySet(), hashMap);
    }

    @Test
    public void testOther() {
        System.setProperty("mail.mime.splitlongparameters", "false");
        Cron cron = cronService.getById("1891762545962807298");
        Map<String, RemoteFileInfo> hashMap = new HashMap<>();
//        hashMap.put(BaseConstant.DEFAULT_MAP_KEY, new RemoteFileInfo
//                ("", 0, "分红信息明细查询.xlsx",
//                        "\\研发\\安联\\TA分红明细\\分红信息明细查询.xlsx", new Date(), new Date(), 1L, "1",
//                        "\\研发\\安联\\TA分红明细"));
        mailInfoService.doSendMailInfo(cron, "2025-03-05", Set.of(BaseConstant.DEFAULT_MAP_KEY), hashMap);
    }

    @Test
    public void testNet(){
        System.setProperty("mail.mime.splitlongparameters", "false");
//        Cron cron = cronService.getById("1895032010431275009");
        JobDataMap jobDataMap = new JobDataMap();
        jobDataMap.put(JobConstant.DATA_DATE, "2025-03-05");
        jobDataMap.put(JobConstant.REMOTE_FILE, "2025-03-05");
        cronService.startJobNow(List.of("1895032010431275009"), jobDataMap);
    }

    @Test
    public void testTA() {
        System.setProperty("mail.mime.splitlongparameters", "false");
        Cron cron = cronService.getById("1891762408687431681");
        Map<String, RemoteFileInfo> hashMap = new LinkedHashMap<>();
//        hashMap.put("6157", new RemoteFileInfo
//                ("", 0, "基金的资金清算报表(基金或销售商)_AZ0057_安联安享22号资产管理产品_2024年03月05日--2024年03月05日.pdf",
//                        "\\研发\\安联\\TA资金清算报表导出\\基金的资金清算报表(基金或销售商)_AZ0057_安联安享22号资产管理产品_2024年03月05日--2024年03月05日.pdf", new Date(), new Date(), 1L, "1",
//                        "\\研发\\安联\\TA资金清算报表导出"));
//        hashMap.put("6163", new RemoteFileInfo
//                ("", 0, "基金的资金清算报表(基金或销售商)_AZ0057_安联安享36号资产管理产品_2024年03月05日--2024年03月05日.pdf",
//                        "\\研发\\安联\\TA资金清算报表导出\\基金的资金清算报表(基金或销售商)_AZ0057_安联安享36号资产管理产品_2024年03月05日--2024年03月05日.pdf", new Date(), new Date(), 1L, "1",
//                        "\\研发\\安联\\TA资金清算报表导出"));
//        hashMap.put("6110", new RemoteFileInfo
//                ("", 0, "基金的资金清算报表(基金或销售商)_AZ0010_安联安享6号资产管理产品_2024年03月05日--2024年03月05日.pdf",
//                        "\\研发\\安联\\TA资金清算报表导出\\基金的资金清算报表(基金或销售商)_AZ0010_安联安享6号资产管理产品_2024年03月05日--2024年03月05日.pdf", new Date(), new Date(), 1L, "1",
//                        "\\研发\\安联\\TA资金清算报表导出"));
//        hashMap.put("6138", new RemoteFileInfo
//                ("", 0, "基金的资金清算报表(基金或销售商)_AZ0038_安联安享18号资产管理产品_2024年03月05日--2024年03月05日.pdf",
//                        "\\研发\\安联\\TA资金清算报表导出\\基金的资金清算报表(基金或销售商)_AZ0038_安联安享18号资产管理产品_2024年03月05日--2024年03月05日.pdf", new Date(), new Date(), 1L, "1",
//                        "\\研发\\安联\\TA资金清算报表导出"));

        mailInfoService.doSendMailInfo(cron, "2025-03-07", hashMap.keySet(), hashMap);
    }

    @Test
    public void getHandler(){
        Map<String, BaseHandler> beansOfType = applicationContext.getBeansOfType(BaseHandler.class);
        log.info("beansOfType:{}", beansOfType);
    }

    @Test
    public void getProductId(){
        List<String> files = List.of("基金的资金清算报表(基金或销售商)_AZ0003_安联资产安享1号资产管理产品_2024年03月05日--2024年03月05日.pdf",
                "基金的资金清算报表(基金或销售商)_AZ0010_安联安享6号资产管理产品_2024年03月05日--2024年03月05日.pdf",
                "基金的资金清算报表(基金或销售商)_AZ0038_安联安享18号资产管理产品_2024年03月05日--2024年03月05日.pdf",
                "基金的资金清算报表(基金或销售商)_AZ0039_安联安享19号资产管理产品_2024年03月05日--2024年03月05日.pdf",
                "基金的资金清算报表(基金或销售商)_AZ0040_安联安享10号资产管理产品_2024年03月05日--2024年03月05日.pdf",
                "基金的资金清算报表(基金或销售商)_AZ0042_安联安享23号资产管理产品_2024年03月05日--2024年03月05日.pdf",
                "基金的资金清算报表(基金或销售商)_AZ0047_安联安益2号资产管理产品_2024年03月05日--2024年03月05日.pdf",
                "基金的资金清算报表(基金或销售商)_AZ0048_安联安享17号资产管理产品_2024年03月05日--2024年03月05日.pdf",
                "基金的资金清算报表(基金或销售商)_AZ0050_安联安益6号资产管理产品_2024年03月05日--2024年03月05日.pdf",
                "基金的资金清算报表(基金或销售商)_AZ0051_安联锐享3号资产管理产品_2024年03月05日--2024年03月05日.pdf",
                "基金的资金清算报表(基金或销售商)_AZ0057_安联安享22号资产管理产品_2024年03月05日--2024年03月05日.pdf",
                "基金的资金清算报表(基金或销售商)_AZ0062_安联安享35号资产管理产品_2024年03月05日--2024年03月05日.pdf",
                "基金的资金清算报表(基金或销售商)_AZ0071_安联安享33号资产管理产品_2024年03月05日--2024年03月05日.pdf",
                "基金的资金清算报表(基金或销售商)_AZ0082_安联锐享10号资产管理产品_2024年03月05日--2024年03月05日.pdf",
                        "基金的资金清算报表(基金或销售商)_AZ0053_安联安益7号资产管理产品_2024年03月07日--2024年03月07日.pdf");
        List<String> valueTables = List.of(
                "证券投资基金估值表_分红PAR_I_自有建行_2024-11-01.xls",
                "证券投资基金估值表_分红PAR_III_自有建行_2024-11-01.xls",
                "证券投资基金估值表_分红PAR_IV_自有建行_2024-11-01.xls",
                "证券投资基金估值表_分红PAR_V_自有建行_2024-11-01.xls"
        );
        Map<String, String> accountInformationMap = accountInformationService.list().stream().collect(Collectors.toMap(AccountInformation::getFullProductName, AccountInformation::getId));
        for (String file : files) {
            String s = StringUtil.extractTAAccountName(file);
            System.out.println(s);
            String s1 = accountInformationMap.get(s);
            System.out.println(s1);
        }
        for (String valueTable : valueTables) {
            String s = StringUtil.extractValuationAccountName(valueTable);
            System.out.println(s);
            String s1 = accountInformationMap.get(s);
            System.out.println(s1);
        }
    }

    @Test
    public void importCustodianBank() {
        Map<String, String> accountInformationMap = accountInformationService.list().stream().collect(Collectors.toMap(AccountInformation::getFullProductName, AccountInformation::getId));
        try (XSSFWorkbook workbook = new XSSFWorkbook("/Users/<USER>/Downloads/5.托管行联系人信息-********-测试.xlsx")) {
            XSSFSheet sheet = workbook.getSheetAt(0);
            Iterator<Row> rowIterator = sheet.rowIterator();
            while (rowIterator.hasNext()) {
                Row row = rowIterator.next();
                int rowNum = row.getRowNum();
                if (rowNum < 3) {
                    continue;
                }
                String name = row.getCell(1).getStringCellValue();
                String role = row.getCell(2).getStringCellValue();
                if (CustodianType.CLEAR.getValue().equals(role)){
                    role = "CLEAR";
                }else if(CustodianType.CALCULATE.getValue().equals(role)){
                    role = "CALCULATE";
                }
                String accountStr = row.getCell(3).getStringCellValue();
                List<String> accountNames = StringUtil.splitStr(accountStr, "、");
                List<String> accountIds = accountNames.stream().map(accountName -> {
                    String id = accountInformationMap.get(accountName);
                    if (id == null) {
                        accountName = accountName.replace("(", "（").replace(")", "）");
                        id = accountInformationMap.get(accountName);
                        log.error("未找到账户信息:{}", accountName);
                    }
                    return id;
                }).collect(Collectors.toList());
                Cell re = row.getCell(4);
                Cell cc = row.getCell(5);
                Cell cell = row.getCell(6);
                CellType cellType = cell.getCellType();
                String phone = "";
                if (CellType.NUMERIC.equals(cellType)) {
                    phone = NumberToTextConverter.toText(cell.getNumericCellValue());
                }
                if (CellType.STRING.equals(cellType)) {
                    phone = cell.getStringCellValue();
                }
                String viewId = IdWorker.getIdStr();
                for (String accountId : accountIds) {
                    if (accountId == null) {
                        continue;
                    }
                    String subId = IdWorker.getIdStr();
                    CustodianBankContactsView custodianBankContactsView = new CustodianBankContactsView();
                    custodianBankContactsView.setId(viewId);
                    custodianBankContactsView.setSubId(subId);
                    CustodianBankContacts custodianBankContacts = new CustodianBankContacts();
                    custodianBankContacts.setId(subId);
                    custodianBankContacts.setProductId(accountId);
                    custodianBankContacts.setCustodianBank(name);
                    custodianBankContacts.setCustodianRole(role);
//                    custodianBankContacts.setRecipient(re.toString());
                    custodianBankContacts.setRecipient("<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>");
//                    custodianBankContacts.setRecipientCc(cc.toString());
                    custodianBankContacts.setRecipientCc("<EMAIL>;<EMAIL>;<EMAIL>");
                    custodianBankContacts.setPhone(phone);
                    custodianBankContactsService.save(custodianBankContacts);
                    custodianBankContactsViewService.save(custodianBankContactsView);
                }
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }


    @Test
    public void importInvestorBank() {
        Map<String, String> accountInformationMap = accountInformationService.list().stream().collect(Collectors.toMap(AccountInformation::getFullProductName, AccountInformation::getId));
        try (XSSFWorkbook workbook = new XSSFWorkbook("/Users/<USER>/Downloads/6.投资人联系人信息-********-测试.xlsx")) {
            XSSFSheet sheet = workbook.getSheetAt(0);
            Iterator<Row> rowIterator = sheet.rowIterator();
            while (rowIterator.hasNext()) {
                Row row = rowIterator.next();
                int rowNum = row.getRowNum();
                if (rowNum < 2) {
                    continue;
                }
                Cell cell1 = row.getCell(1);
                if (cell1 == null) {
                    continue;
                }
                String name = cell1.getStringCellValue();
                String accountStr = row.getCell(2).getStringCellValue();
                List<String> accountNames = StringUtil.splitStr(accountStr, "、");
                List<String> accountIds = accountNames.stream().map(accountName -> {
                    String id = accountInformationMap.get(accountName);
                    if (id == null) {
                        log.error("未找到投资人账户信息:{}", accountName);
                    }
                    return id;
                }).collect(Collectors.toList());
                String contentStr = row.getCell(3).getStringCellValue();
                Map<String, List<String>> methodHandlerMap = getStringListMap(contentStr);
                String viewId = IdWorker.getIdStr();
                for (String accountId : accountIds) {
                    if (accountId == null) {
                        continue;
                    }
                    for (Map.Entry<String, List<String>> entry : methodHandlerMap.entrySet()) {
                        String method = entry.getKey();
                        List<String> handlers = entry.getValue();
                        for (String handler : handlers) {
                            String subId = IdWorker.getIdStr();
                            InvestorContactsView investorContactsView = new InvestorContactsView();
                            investorContactsView.setId(viewId);
                            investorContactsView.setSubId(subId);
                            InvestorContacts investorContacts = new InvestorContacts();
                            investorContacts.setId(subId);
                            investorContacts.setProductId(accountId);
                            investorContacts.setInvestor(name);
//                            investorContacts.setRecipient("<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>");
                            investorContacts.setRecipient("<EMAIL>");
//                            investorContacts.setRecipientCc("<EMAIL>;<EMAIL>;<EMAIL>");
                            investorContacts.setRecipientCc("");
                            investorContacts.setMethod(method);
                            investorContacts.setHandler(handler);
                            investorContactsService.save(investorContacts);
                            investorContactsViewService.save(investorContactsView);
                        }
                    }
                }
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @NotNull
    private static Map<String, List<String>> getStringListMap(String contentStr) {
        Map<String, List<String>> methodHandlerMap = new HashMap<>();
        List<String> mailHandlerList = new ArrayList<>();
        List<String> sztHandlerList = new ArrayList<>();
        String[] contentSplit = contentStr.split("、");
        for (String content : contentSplit) {
            switch (content) {
                case "邮件发送-Excel 估值表发送":
                    mailHandlerList.add(EXCEL_VALUATION_TABLE.name());
                    break;
                case "邮件发送-净值发送":
                    mailHandlerList.add(NET_VALUE.name());
                    break;
                case "深证通发送-Excel 估值表发送":
                    sztHandlerList.add(EXCEL_VALUATION_TABLE.name());
                    break;
                case "深证通发送-DBF 估值表发送":
                    sztHandlerList.add(DBF_VALUATION_TABLE.name());
                    break;
                case "邮件发送-行情表":
                    mailHandlerList.add(MARKET_PRICE.name());
                    break;
            }
        }

        methodHandlerMap.put(DisclosureMethod.MAIL.name(), mailHandlerList);
        methodHandlerMap.put(DisclosureMethod.SZT.name(), sztHandlerList);
        return methodHandlerMap;
    }

    @Test
    public void testValuation() {
        LambdaQueryWrapper<NetValueDisclosure> netValueDisclosureLambdaQueryWrapper = new LambdaQueryWrapper<>();
        netValueDisclosureLambdaQueryWrapper.eq(NetValueDisclosure::getProductId, "2101");
        netValueDisclosureLambdaQueryWrapper.eq(NetValueDisclosure::getValuationDate, "2025-02-27");
        NetValueDisclosure one = netValueDisclosureService.getOne(netValueDisclosureLambdaQueryWrapper);
        //valuationTableHandler.dealValuationTable(one);
        System.out.println(one);
    }

    /**
     * 测试发送邮件这一个动作
     */
    @Test
    public void testSendMail() {
        try {
            mailUtil.sendEmailFileFromShareFolder(CollectionUtil.newArrayList("<EMAIL>"), null, null, "测试邮件", "测试邮件正文");
        } catch (Exception ignore) {
        }
    }

    @Test
    public void dealThirdFile(){
        Resource resource = resourceLoader.getResource("classpath:third/wind净值对接--模板.xlsx");
        try (InputStream inputStream = resource.getInputStream();
             FileOutputStream fos = new FileOutputStream("/Users/<USER>/Downloads/万得净值对接--模板--复制.xlsx");
             XSSFWorkbook workbook = new XSSFWorkbook(inputStream)) {
            workbook.cloneSheet(2, "净值录入2");
            workbook.write(fos);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }



}
