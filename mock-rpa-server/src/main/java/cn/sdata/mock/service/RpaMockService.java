package cn.sdata.mock.service;

import cn.sdata.mock.config.MockRpaProperties;
import cn.sdata.mock.model.FlowConfig;
import cn.sdata.mock.model.RpaRequest;
import cn.sdata.mock.model.RpaResponse;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * RPA Mock 服务
 * 
 * <AUTHOR> RPA Server
 */
@Service
public class RpaMockService {
    
    @Autowired
    private MockRpaProperties mockRpaProperties;

    private final ObjectMapper objectMapper = new ObjectMapper();

    // 存储execId的查询次数
    private final Map<String, AtomicInteger> execIdQueryCount = new ConcurrentHashMap<>();
    
    /**
     * 处理RPA请求
     */
    public List<RpaResponse> processRequest(List<RpaRequest> requests) {
        if (requests == null || requests.isEmpty()) {
            return Collections.emptyList();
        }
        
        // 解析请求参数
        Map<String, Object> params = parseRequestParams(requests);
        String method = (String) params.get("{2881E26D-62CE-4937-B4BB-8998440417C4}");
        
        if ("GetFlowIDByFullPath".equals(method)) {
            return handleGetFlowIdByFullPath(params);
        } else if ("StartFlow".equals(method)) {
            return handleStartFlow(params);
        } else if ("GetFlowExecedState".equals(method)) {
            return handleGetFlowExecedState(params);
        }
        
        return Collections.emptyList();
    }
    
    /**
     * 解析请求参数
     */
    private Map<String, Object> parseRequestParams(List<RpaRequest> requests) {
        Map<String, Object> params = new HashMap<>();
        for (RpaRequest request : requests) {
            params.put(request.getName(), request.getValue());
        }
        return params;
    }
    
    /**
     * 处理获取流程ID请求
     */
    private List<RpaResponse> handleGetFlowIdByFullPath(Map<String, Object> params) {
        String flowPath = (String) params.get("FlowPath");
        
        // 查找匹配的流程配置
        for (FlowConfig flowConfig : mockRpaProperties.getFlowPaths()) {
            if (flowConfig.getPath().equals(flowPath)) {
                return Arrays.asList(
                    new RpaResponse(4, "FlowID", flowConfig.getFlowId())
                );
            }
        }
        
        // 未找到匹配的流程
        return Arrays.asList(
            new RpaResponse(4, "{50043442-8A69-4A6B-A8B5-61F882EDE4F3}", "流程路径不存在: " + flowPath)
        );
    }
    
    /**
     * 处理启动流程请求
     */
    private List<RpaResponse> handleStartFlow(Map<String, Object> params) {
        String flowId = (String) params.get("FlowID");
        
        // 生成32位大写十六进制execId
        String execId = generateExecId();
        
        // 初始化查询次数
        execIdQueryCount.put(execId, new AtomicInteger(0));
        
        return Arrays.asList(
            new RpaResponse(4, "ExecID", execId)
        );
    }
    
    /**
     * 处理获取流程执行状态请求
     */
    private List<RpaResponse> handleGetFlowExecedState(Map<String, Object> params) {
        String flowIds = (String) params.get("IDs");
        String dbDate = (String) params.get("DBDate");
        
        // 模拟返回流程执行状态数据结构
        List<List<RpaResponse>> flowDataList = new ArrayList<>();
        
        // 这里简化处理，实际应该根据flowIds查询对应的execId
        // 为了演示，我们使用一个模拟的execId
        String mockExecId = generateMockExecIdForQuery();
        
        if (mockExecId != null) {
            AtomicInteger queryCount = execIdQueryCount.get(mockExecId);
            if (queryCount != null) {
                int currentCount = queryCount.incrementAndGet();
                
                String execState;
                String execResult;
                
                if (currentCount <= mockRpaProperties.getExecution().getExecutingCount()) {
                    // 前9次返回正在执行
                    execState = "-1";
                    execResult = "正在执行中";
                } else {
                    // 第10次及以后返回执行成功
                    execState = "1";
                    execResult = mockRpaProperties.getExecution().getSuccessMessage();
                }
                
                // 构造返回数据
                List<RpaResponse> execData = Arrays.asList(
                    new RpaResponse(4, "Value", mockExecId),      // ExecID
                    new RpaResponse(4, "Value", ""),             // 其他字段...
                    new RpaResponse(4, "Value", "手工执行"),
                    new RpaResponse(4, "Value", flowIds),        // FlowID
                    new RpaResponse(4, "Value", "Mock执行"),
                    new RpaResponse(4, "Value", ""),
                    new RpaResponse(4, "Value", ""),
                    new RpaResponse(4, "Value", ""),
                    new RpaResponse(4, "Value", ""),
                    new RpaResponse(4, "Value", execState),      // 执行状态
                    new RpaResponse(4, "Value", ""),
                    new RpaResponse(4, "Value", ""),
                    new RpaResponse(4, "Value", execResult)      // 执行结果
                );
                
                flowDataList.add(execData);
            }
        }
        
        // 构造完整的响应结构
        return Arrays.asList(
            new RpaResponse(19, "k_flow", flowDataList)
        );
    }
    
    /**
     * 生成32位大写十六进制execId
     */
    private String generateExecId() {
        return UUID.randomUUID().toString().replace("-", "").toUpperCase();
    }
    
    /**
     * 获取用于查询的模拟execId（简化处理）
     */
    private String generateMockExecIdForQuery() {
        // 简化处理：返回第一个存在的execId
        return execIdQueryCount.keySet().stream().findFirst().orElse(null);
    }

    /**
     * 解析JSON字符串为RpaRequest列表
     */
    public List<RpaRequest> parseJsonToRequests(String jsonStr) {
        try {
            return objectMapper.readValue(jsonStr, new TypeReference<List<RpaRequest>>() {});
        } catch (Exception e) {
            System.out.println("解析JSON失败: " + e.getMessage());
            return Collections.emptyList();
        }
    }

    /**
     * 创建错误响应
     */
    public List<RpaResponse> createErrorResponse(String errorMessage) {
        return Arrays.asList(
            new RpaResponse(4, "{50043442-8A69-4A6B-A8B5-61F882EDE4F3}", errorMessage)
        );
    }
}
