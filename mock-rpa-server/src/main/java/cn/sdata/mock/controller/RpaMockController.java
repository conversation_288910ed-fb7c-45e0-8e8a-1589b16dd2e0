package cn.sdata.mock.controller;

import cn.sdata.mock.model.RpaRequest;
import cn.sdata.mock.model.RpaResponse;
import cn.sdata.mock.service.RpaMockService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * RPA Mock 控制器
 * 
 * <AUTHOR> RPA Server
 */
@RestController
public class RpaMockController {
    
    @Autowired
    private RpaMockService rpaMockService;
    
    /**
     * 模拟RPA接口调用
     * 接口路径: /CallFunc.aom
     * 请求方式: POST
     * 请求格式: JSON数组
     * 响应格式: JSON数组
     */
    @PostMapping(value = "/CallFunc.aom", 
                 consumes = MediaType.APPLICATION_JSON_VALUE,
                 produces = MediaType.APPLICATION_JSON_VALUE)
    public List<RpaResponse> callFunc(@RequestBody List<RpaRequest> requests) {
        
        System.out.println("=== Mock RPA Server 收到请求 ===");
        System.out.println("请求参数: " + requests);
        
        List<RpaResponse> responses = rpaMockService.processRequest(requests);
        
        System.out.println("响应结果: " + responses);
        System.out.println("=== 请求处理完成 ===");
        
        return responses;
    }
    
    /**
     * 健康检查接口
     */
    @GetMapping("/health")
    public String health() {
        return "Mock RPA Server is running!";
    }
    
    /**
     * 获取服务信息
     */
    @GetMapping("/info")
    public String info() {
        return "Mock RPA Server v1.0.0 - 模拟RPA服务器，支持流程启动和状态查询";
    }
}
