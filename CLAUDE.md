# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Build and Development Commands

### Building the Application
```bash
# Build the project
mvn clean package

# Run the application
mvn spring-boot:run

# Build without tests
mvn clean package -DskipTests
```

### Testing
```bash
# Run all tests
mvn test

# Run specific test class
mvn test -Dtest=ClassName

# Run specific test method
mvn test -Dtest=ClassName#methodName
```

### Database Operations
The application uses multiple databases (MySQL primary, Oracle for valuation/TA data). Database configurations are in `application.yml` under `spring.datasource.dynamic`.

## High-Level Architecture

### Core Business Domain
This is a financial operations management system for asset management operations, specifically handling:

- **Net Value Disclosure**: Daily net asset value calculations and reporting for investment funds
- **Bank Reconciliation**: Automated reconciliation processes for custodian bank data
- **Fund Confirmations**: Processing of open fund transaction confirmations and reconciliations  
- **RPA Integration**: Robotic Process Automation for data extraction from external systems
- **Mail Processing**: Automated email handling for financial reports and notifications
- **Portfolio Audit**: Monitoring of portfolio net value fluctuations and warnings

### Key Architectural Patterns

#### 1. Job-based Processing Architecture
The system is built around scheduled jobs using Quartz:
- **`job/`** package contains scheduled job implementations
- Jobs are annotated with `@TradeDay` to run only on trading days
- Complex job orchestration using job groups and dependencies
- Extensive logging of job execution via `BaseCronLog` entities

#### 2. Handler Pattern for Business Logic
- **`disclosure/`** handlers process different types of financial disclosures
- **`rpa/`** handlers manage external system integrations
- **`open/`** handlers process fund confirmation documents
- All handlers implement common interfaces for consistent processing

#### 3. Multi-Database Strategy
- **master**: Primary MySQL database for application data
- **valuation**: Oracle database for valuation calculations
- **ta**: Oracle database for fund transaction data  
- **cop/o32**: Additional Oracle databases for specific business functions
- Uses MyBatis-Plus with dynamic datasource switching

#### 4. Mail Processing Pipeline
Sophisticated email processing system:
- Rule-based email picking and filtering
- Template-driven email generation
- Macro expansion for dynamic content
- Attachment processing and file handling

### Package Structure Overview

- **`controller/`** - REST API endpoints organized by business domain
- **`service/`** - Business logic layer with comprehensive service implementations
- **`mapper/`** - MyBatis data access layer with XML mapping files
- **`entity/`** - JPA entities representing database tables and business objects
- **`job/`** - Scheduled job implementations for automated processing
- **`rpa/`** - RPA integration handlers for external system automation
- **`disclosure/`** - Financial disclosure processing components
- **`audit/`** - Portfolio monitoring and warning system
- **`config/`** - Spring configuration classes for various integrations
- **`utils/`** - Utility classes for common operations (Excel, encryption, etc.)

### Integration Points

#### External Systems
- **RPA Platform**: Automated data extraction via HTTP API calls
- **SMB/Network Shares**: File system integration for document processing
- **Email Systems**: IMAP/SMTP integration for automated email processing
- **OCR Service**: Tencent Cloud OCR for document text extraction
- **FTP Servers**: File transfer operations for data exchange

#### Key Configuration Areas
- **Quartz Scheduler**: Job scheduling and clustering configuration
- **Database Connections**: Multi-database configuration with connection pooling
- **Security**: Sa-Token JWT authentication with LDAP integration
- **File Processing**: Various file paths and processing configurations
- **Email**: SMTP/IMAP configuration for automated email operations

### Important Development Notes

#### Database Schema Management
- Quartz tables use `qrtz_` prefix
- Application uses soft delete pattern (logic-delete-value: 1)
- MyBatis-Plus handles entity mapping with enum type handlers

#### Error Handling
- Custom exception hierarchy with `BusinessException`, `SMBException`, `TaskException`
- Global exception handler in `GlobalExceptionHandler`
- Extensive logging throughout the application

#### Security Considerations
- Configuration files contain database credentials and API keys
- LDAP integration for user authentication
- JWT token-based API authentication
- Rate limiting configuration available

### Testing Strategy
Limited test coverage with focus on:
- Job execution testing
- Mail service testing  
- Audit functionality testing
- Use standard Spring Boot testing annotations and practices